# Geolocation-Based Dynamic Agreement Link System

## Overview

This system provides dynamic Master Customer Agreement (MCA) links in forms based on the user's geolocation. The system automatically detects the user's location and displays the appropriate legal agreement based on their country and business region.

## Architecture Components

### 1. Geolocation Detection (`/api/geo-location/`)

**File:** `src/app/api/geo-location/route.ts`

The API endpoint uses Vercel's geolocation headers to determine user location:

```typescript
export async function GET(request: NextRequest) {
    const continent: string = request?.headers?.get('x-vercel-ip-continent') || ''
    const country: string = request?.headers?.get('x-vercel-ip-country') || ''
    const city: string = request?.headers?.get('x-vercel-ip-city') || ''
    const geo = geolocation(request)
    const region = CONTINENT_REGION_MAP[continent] || 'AMER'

    return NextResponse.json({ continent, country, city, geo, region })
}
```

### 2. Redux Store Management

**File:** `src/redux/slices/appSlice.ts`

The geolocation data is stored in Redux state:

```typescript
interface AppSliceState {
  geoLocationData: {
    countryCode?: string
    countryName?: string
    businessRegion?: string
    currentPageRegion?: string
  }
}

// Action to set geolocation data
setGeoLocationData: (state, action: PayloadAction<any>) => {
  state.geoLocationData = action.payload
}
```

### 3. Geolocation Notification Component

**File:** `src/components/NotificationCenter/GeoLocationNotification/index.tsx`

This component handles:
- Fetching geolocation data from API
- Setting Redux store with geolocation information
- Determining current page region from URL
- Showing notifications when user is in wrong region

**Key Function:**
```typescript
dispatch(setGeoLocationData({
  countryCode,
  countryName,
  businessRegion,
  currentPageRegion,
}))
```

### 4. Agreement Link Resolution

**File:** `src/systems/AltusForms/Form/utils.tsx`

#### `getAgreementLinkByCountry` Function

Determines the appropriate MCA link based on country and region:

```typescript
export const getAgreementLinkByCountry = ({
  countryCode,
  businessRegion
}: {
  countryCode?: string
  businessRegion?: string
}) => {
  const upperCountry = countryCode?.toUpperCase()
  
  // Check if country is in Americas (US or Canada)
  const isAmer = ['US', 'CA'].includes(upperCountry ?? '')
  // Check if country is in Australia/New Zealand
  const isAnz = ['AU', 'NZ'].includes(upperCountry ?? '')
  // Check if region is EMEA or APAC (excluding ANZ countries)
  const isEMEAorAPACExclAnz = (businessRegion === 'EMEA' || businessRegion === 'APAC') && !isAnz

  // Return appropriate MCA link based on region
  if (isAmer) return MCA_LINKS.USA
  if (isAnz) return MCA_LINKS.AU
  if (isEMEAorAPACExclAnz) return MCA_LINKS.UK

  return MCA_LINKS.USA // default fallback USA
}
```

#### `getfileIdByMcaUrl` Function

Retrieves the Contentful file ID for a given MCA URL:

```typescript
export const getfileIdByMcaUrl = async (mcaUrl: string): Promise<string | null> => {
  if (!mcaUrl) return null
  const fileId = await getAgreementDetail(mcaUrl)
  return fileId
}
```

### 5. MCA Links Configuration

**File:** `src/lib/constant.ts`

Defines the available MCA links:

```typescript
export const MCA_LINKS = {
  USA: '/downloads/legal/agreements/master-customer-agreement-us.pdf',
  UK: '/downloads/legal/agreements/master-customer-agreement-uk.pdf',
  AU: '/downloads/legal/agreements/master-customer-agreement-aus.pdf',
  FORBURY: '/downloads/legal/Forbury/ForburyAddendum.pdf'
}
```

### 6. Region Mapping

**File:** `src/globals/utils.tsx`

Maps continents to business regions:

```typescript
export const CONTINENT_REGION_MAP: Record<string, 'APAC' | 'EMEA' | 'AMER'> = {
  // Americas
  NA: 'AMER',
  SA: 'AMER',
  // EMEA
  EU: 'EMEA',
  AF: 'EMEA',
  // APAC
  AS: 'APAC',
  OC: 'APAC',
}

export const REGION_TO_SLUG_MAP: Record<string, string> = {
  APAC: 'au',
  EMEA: 'uk',
  AMER: 'us',
}
```

## Form Integration

### Dynamic Agreement in Forms

Forms can enable dynamic agreements by setting `isDynamicAgreement: true` in the form configuration.

**File:** `src/systems/AltusForms/Form/utils.tsx`

During form submission, if `isDynamicAgreement` is enabled:

```typescript
// FOR geo location based agreement link
let agreementId
if (formData?.isDynamicAgreement) {
  try {
    const storeData = store.getState()
    const countryCode = storeData?.app?.geoLocationData?.countryCode
    const businessRegion = storeData?.app?.geoLocationData?.businessRegion

    agreementId = await getfileIdByMcaUrl(getAgreementLinkByCountry({
      countryCode: countryCode,
      businessRegion: businessRegion,
    }))

    if (!agreementId) {
      console.error('agreementId not found')
      setFormSubmitionState('FAILED')
      return
    }
  } catch (error) {
    setFormSubmitionState('FAILED')
    console.error('Error fetching agreementId:', error)
    return
  }
}
```

### Checkbox Input with Dynamic Agreement

**File:** `src/systems/AltusForms/Form/inputType.tsx`

When `isDynamicAgreement` is true, the checkbox shows dynamic links:

```typescript
{isDynamicAgreement ? 
  <label style={{ padding: '0', width: '100%' }} htmlFor={randomId}>
    I accept the <a href={getAgreementLinkByCountry({
      countryCode: geoLocationData?.countryCode,
      businessRegion: geoLocationData?.businessRegion,
    })} target='_blank'>Master Customer Agreement</a>
    {" "}and <a href={MCA_LINKS.FORBURY} target='_blank'>Forbury Addendum</a>.
  </label> :
  <label style={{ padding: '0', width: '100%' }} htmlFor={randomId}>
    {input?.richLabel ? (
      <Richtext data={input?.richLabel} />
    ) : (
      input?.altusLabel
    )}
  </label>
}
```

## Agreement File Resolution

**File:** `actions/getAgreementDetail.ts`

The `getAgreementDetail` function retrieves file IDs from Contentful Assets:

```typescript
export async function getAgreementDetail(mcaUrl: string): Promise<string | null> {
  try {
    const domainShortName = getDomainShortName(process.env.NEXT_PUBLIC_DOMAIN || '')
    const assetsData = await getConfigurationByScopeAndType('Assets', domainShortName.toUpperCase())
    
    // Extract path from MCA URL and normalize
    let urlPath = mcaUrl.startsWith('/') ? mcaUrl : `/${mcaUrl}`
    const normalizedUrlPath = urlPath.replace(/\.pdf$/, '')
    
    // Find matching asset
    const matchingAsset = assetsData.find((asset: any) => {
      const normalizedSource = (asset?.source || '').replace(/\.pdf$/, '')
      return normalizedSource === normalizedUrlPath
    })
    
    return matchingAsset?.fileId || null
  } catch (error) {
    console.error('Error fetching fileId by MCA URL:', error)
    return null
  }
}
```

## Usage Flow

1. **Page Load**: `GeoLocationNotification` component fetches user's geolocation
2. **Redux Update**: Geolocation data is stored in Redux state via `setGeoLocationData`
3. **Form Rendering**: Forms with `isDynamicAgreement: true` show dynamic agreement links
4. **Form Submission**: System retrieves appropriate agreement file ID based on geolocation
5. **Validation**: Form submission fails if agreement ID cannot be resolved

## Error Handling

- **API Failure**: Falls back to default 'AMER' region and 'US' country
- **Missing Agreement**: Form submission fails with 'FAILED' state
- **File ID Resolution**: Returns null if no matching asset found
- **Redux Fallback**: Uses cached geolocation data if available

## Configuration Requirements

1. **Environment**: Requires Vercel deployment for geolocation headers
2. **Contentful**: Assets must be configured with proper source paths
3. **Domain**: Domain short name must be configured for asset resolution
4. **Form Setup**: Forms must have `isDynamicAgreement: true` to enable feature

## Testing Considerations

- Test with different IP locations (VPN)
- Verify fallback behavior when geolocation fails
- Test form submission with missing agreement files
- Validate Redux state management
- Check error handling for API failures
