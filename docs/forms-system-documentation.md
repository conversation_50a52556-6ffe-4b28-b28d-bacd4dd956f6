# Forms System (FormRouter) Documentation

## Description / Overview

The Forms System is a comprehensive form management solution built for the Altus Group website, providing dynamic form creation, validation, submission, and analytics tracking across multiple form templates and use cases. The system features intelligent routing, multi-step forms, exit-intent popups, floating forms, and seamless integration with marketing automation platforms including SFMC (Salesforce Marketing Cloud), Pardot, and Reonomy.

### Key Features

- **Multi-Template Support**: 9 different form templates (Inline, Popup, Floating, Multi-Step, Hero, Exit Intent, etc.)
- **Advanced Validation**: Real-time email validation, custom field validation, and error handling
- **Marketing Integration**: SFMC, Pardot, and Reonomy API integrations
- **Analytics Tracking**: Comprehensive GA4 event tracking and conversion monitoring
- **Responsive Design**: Mobile-optimized forms with touch-friendly interfaces
- **Accessibility Compliance**: WCAG 2.1 AA compliant with full keyboard navigation
- **Exit Intent Detection**: Smart popup forms triggered by user behavior
- **Multi-Step Forms**: Progressive form completion with step validation
- **Real-time Feedback**: Toast notifications, loading states, and success messages

## Contributors

### Development Team

- **Frontend Engineers**: React/Next.js form specialists
- **Backend Engineers**: API integration and data processing specialists
- **UX/UI Designers**: Form interface and user experience designers
- **Analytics Engineers**: GA4 and conversion tracking implementation
- **Marketing Engineers**: SFMC and marketing automation integration
- **DevOps Engineers**: Form submission infrastructure and monitoring

### Stakeholders

- **Marketing Team**: Lead generation and campaign tracking requirements
- **Sales Team**: Lead qualification and CRM integration needs
- **Content Managers**: Form configuration and content management
- **Product Managers**: Form conversion optimization and user experience
- **Compliance Team**: Data privacy and accessibility requirements

## Functionality

### Core Components

#### 1. FormRouter (`FormRouter.tsx`)

Central routing component that determines which form template to render based on configuration.

**Supported Templates:**

- `Inline` - Standard inline forms embedded in content
- `Popup` - Modal popup forms triggered by user actions
- `Floating` - Floating newsletter subscription forms
- `Floating - Home` - Homepage-specific floating forms
- `FloatingComponent` - Insights page floating forms
- `ReonomyFreeTrial` - Reonomy product trial registration
- `MultiStep` - Multi-step form wizard with progress tracking
- `FormHero` - Hero section forms with prominent placement
- `ExitIntent` - Exit-intent popup forms for lead capture

#### 2. Form Templates

##### Inline Forms (`Form/index.tsx`)

- **Purpose**: Standard forms embedded within page content
- **Features**: Full field validation, custom styling, responsive design
- **Use Cases**: Contact forms, demo requests, newsletter signups
- **Analytics**: Form start, completion, and error tracking

##### Popup Forms

- **Purpose**: Modal forms triggered by user interactions
- **Features**: Overlay display, close functionality, focus management
- **Use Cases**: Lead magnets, content gating, promotional offers
- **Integration**: Same core functionality as inline forms

##### Floating Forms (`FormFloating/index.tsx`)

- **Purpose**: Persistent newsletter subscription forms
- **Features**: Minimalist design, session storage integration, auto-hide on success
- **Use Cases**: Newsletter subscriptions, content updates, blog notifications
- **Behavior**: Stores subscription status to prevent re-appearance

##### Multi-Step Forms (`FormMultiStep/index.tsx`)

- **Purpose**: Complex forms broken into manageable steps
- **Features**: Progress indicators, step validation, data persistence
- **Use Cases**: Product trials, detailed registrations, onboarding flows
- **Navigation**: Forward/backward navigation with validation checkpoints

##### Exit Intent Forms (`FormExitIntent/index.tsx`)

- **Purpose**: Capture leads when users attempt to leave the page
- **Features**: Mouse movement detection, browser back button detection, session tracking
- **Use Cases**: Last-chance offers, newsletter signups, feedback collection
- **Triggers**: Mouse leave events, browser navigation attempts, inactivity timers

##### Hero Forms (`FormHero/index.tsx`)

- **Purpose**: Prominent forms in hero sections
- **Features**: Horizontal layout, prominent CTAs, streamlined design
- **Use Cases**: Landing page conversions, product demos, trial signups
- **Design**: Optimized for high visibility and conversion

#### 3. Form Field System

##### Supported Field Types

- **Text Fields**: Standard text input with validation
- **Email Fields**: Email validation with domain restrictions
- **Phone Fields**: Numeric input with formatting
- **Dropdown Fields**: Single and multi-select options
- **Checkbox Fields**: Multiple selection with array handling
- **Hidden Fields**: UTM parameters and tracking data
- **Textarea Fields**: Multi-line text input
- **Radio Fields**: Single selection from options

##### Field Configuration

```typescript
interface FormField {
  altusFieldType: 'Text' | 'Email' | 'Dropdown' | 'Checkbox' | 'Hidden'
  altusFieldName: string
  altusLabel: string
  altusIsRequired: boolean
  placeholderText: string
  validationErrorMessage: string
  helperText: string
  isAltusEmailAllowed: boolean
  isGenericEmailAllowed: boolean
}
```

### Form Validation System

#### 1. Email Validation

- **Domain Restrictions**: Block/allow specific email domains
- **Generic Email Handling**: Control generic email providers
- **Real-time Validation**: Debounced validation during typing
- **Custom Error Messages**: Configurable validation messages

#### 2. Required Field Validation

- **Client-side Validation**: Immediate feedback on required fields
- **Form Submission Blocking**: Prevent submission with validation errors
- **Visual Indicators**: Error styling and messaging
- **Accessibility**: Screen reader compatible error announcements

#### 3. Custom Validation Patterns

- **Phone Number Formatting**: Automatic formatting and validation
- **Text Length Limits**: Minimum and maximum character validation
- **Pattern Matching**: Regex-based validation for specific formats
- **Cross-field Validation**: Dependent field validation logic

### Form Submission Processing

#### 1. Data Collection and Processing

```typescript
interface FormSubmissionData {
  // User-entered data
  email: string
  first_name: string
  last_name: string
  company: string

  // Tracking data
  utm_campaign: string
  utm_source: string
  utm_medium: string
  sourceUrl: string
  formid: string
  clientid: string
  measurementid: string
}
```

#### 2. Integration Endpoints

##### SFMC (Salesforce Marketing Cloud) Integration

- **Purpose**: Marketing automation and lead nurturing
- **Data Mapping**: Form fields to SFMC data extensions
- **Tracking**: Campaign attribution and lead scoring
- **Error Handling**: Retry logic and fallback mechanisms

##### Pardot Integration

- **Purpose**: B2B lead management and scoring
- **Features**: Lead qualification, campaign tracking, ROI measurement
- **Data Processing**: Form data transformation for Pardot API
- **Analytics**: Conversion tracking and attribution

##### Reonomy Integration

- **Purpose**: Product trial registration and user management
- **Features**: Account creation, trial activation, user onboarding
- **Security**: Secure API communication and data handling
- **Validation**: Enhanced validation for trial registrations

## UI Components

### Form Layout Components

#### 1. Form Container

- **Purpose**: Consistent form styling and layout
- **Features**: Responsive design, shadow effects, padding management
- **Styling**: SCSS modules with theme support
- **Accessibility**: Proper form landmarks and structure

#### 2. Input Components

- **Text Inputs**: Styled text fields with validation states
- **Dropdown Selects**: Custom-styled select components
- **Checkbox Groups**: Multi-select checkbox arrays
- **Button Components**: Form submission and navigation buttons

#### 3. Feedback Components

- **Toast Notifications**: Success, error, and loading messages
- **Progress Indicators**: Multi-step form progress tracking
- **Validation Messages**: Inline error and helper text
- **Loading States**: Form submission progress indicators

### Responsive Design Features

#### 1. Mobile Optimization

- **Touch-friendly Inputs**: Larger touch targets for mobile devices
- **Responsive Layouts**: Adaptive form layouts for different screen sizes
- **Mobile-specific Behaviors**: Keyboard optimization, focus management
- **Performance**: Optimized for mobile network conditions

#### 2. Accessibility Features

- **ARIA Labels**: Comprehensive labeling for screen readers
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Proper focus handling and visual indicators
- **Color Contrast**: WCAG 2.1 AA compliant color schemes
- **Screen Reader Support**: Optimized for assistive technologies

## Codebase Integration

### File Structure

```
src/
├── lib/componentsRouter/
│   ├── FormRouter.tsx                # Main form routing component
│   └── FormFieldRouter.tsx           # Form field routing component
├── systems/AltusForms/
│   ├── Form/                         # Core form component
│   │   ├── index.tsx
│   │   ├── interface.ts
│   │   ├── defaults.tsx
│   │   ├── utils.tsx
│   │   ├── inputType.tsx
│   │   └── @core/
│   │       ├── EmailField.tsx
│   │       └── AllFormsRender.tsx
│   ├── FormFloating/                 # Floating form components
│   ├── FormFloatingHome/             # Homepage floating forms
│   ├── FormFloatingInsights/         # Insights page forms
│   ├── FormMultiStep/                # Multi-step form wizard
│   ├── FormHero/                     # Hero section forms
│   └── FormExitIntent/               # Exit intent popup forms
├── redux/slices/
│   ├── formSlice.ts                  # Form state management
│   └── popupSlice.ts                 # Popup state management
├── lib/propsMapping/
│   └── form.mapping.ts               # Form props transformation
├── lib/queries/
│   └── form.query.ts                 # GraphQL form queries
└── utils/
    └── analyticsEvents.ts            # Form analytics tracking
```

### Key Dependencies

- **React 18+**: Component framework with hooks
- **Next.js 14**: Application framework and routing
- **Redux Toolkit**: Form state management
- **Contentful**: Headless CMS for form configuration
- **Framer Motion**: Form animations and transitions
- **Bootstrap**: Form styling and validation classes
- **Moment.js**: Date handling and formatting

### Integration Points

#### 1. Contentful CMS Integration

- **Content Types**: ComponentForm for form configuration
- **Field Management**: Dynamic form field configuration
- **Template Selection**: Form template and styling options
- **Content Delivery**: Real-time form updates via API

#### 2. Redux State Management

- **Form Slice**: Form values, validation state, submission status
- **Popup Slice**: Exit intent and modal form state
- **Session Management**: Form activation and completion tracking
- **Data Persistence**: Form data preservation across sessions

#### 3. Analytics Integration

- **GA4 Events**: Form interaction and conversion tracking
- **Custom Events**: Form-specific analytics events
- **Conversion Tracking**: Lead generation and ROI measurement
- **User Behavior**: Form abandonment and completion analysis

## Vercel Deployment

### Environment Configuration

```bash
# Form Integration Endpoints
SFMC_ENDPOINT_URL=https://your-sfmc-endpoint.com
PARDOT_ENDPOINT_URL=https://your-pardot-endpoint.com
REONOMY_API_ENDPOINT=https://api.reonomy.com

# Analytics Configuration
NEXT_PUBLIC_GA4_MEASUREMENT_ID=G-XXXXXXXXXX
GA4_API_SECRET=your_ga4_api_secret

# Contentful Configuration
CONTENTFUL_SPACE_ID=your_space_id
CONTENTFUL_ACCESS_TOKEN=your_access_token

# Security Configuration
FORM_ENCRYPTION_KEY=your_encryption_key
API_RATE_LIMIT=100
```

### Build Configuration

- **Node.js Version**: 18.x
- **Build Command**: `yarn build`
- **Form Validation**: Build-time form configuration validation
- **Asset Optimization**: Form-related asset optimization

### Performance Optimizations

- **Code Splitting**: Form components loaded on demand
- **Bundle Analysis**: Form-specific bundle optimization
- **Caching Strategy**: Form configuration and validation caching
- **CDN Integration**: Form asset delivery optimization

## GitHub Repository Structure

### Branch Strategy

- **main**: Production-ready form code
- **develop**: Integration branch for form features
- **feature/forms-\***: Form-specific feature branches
- **hotfix/forms-\***: Critical form bug fixes

### Key Files and Directories

```
├── .github/workflows/          # CI/CD pipelines
├── src/systems/AltusForms/     # Form system components
├── src/lib/componentsRouter/   # Form routing logic
├── src/redux/slices/          # Form state management
├── docs/forms-*               # Form system documentation
├── tests/forms/               # Form-specific tests
└── package.json               # Form dependencies
```

### Development Workflow

1. **Feature Branch**: Create from develop
2. **Form Development**: Local development with hot reload
3. **Form Testing**: Unit and integration tests
4. **Code Review**: Pull request review process
5. **Integration**: Merge to develop branch
6. **Staging Deployment**: Automatic deployment to staging
7. **Production**: Manual promotion to production

## Contentful Configuration

### Content Models

#### 1. ComponentForm Content Type

```graphql
{
  template: String              # Form template type
  endpointUrl: String          # Form submission endpoint
  sfmcUrl: String              # SFMC integration URL
  formCategory: String         # Form categorization
  header: RichText             # Form header content
  footer: RichText             # Form footer content
  thankYouMessage: RichText    # Success message
  loadingMessage: RichText     # Loading state message
  successMessage: RichText     # Success state message
  errorMessage: RichText       # Error state message
  backgroundImage: Asset       # Form background image
  displayAt: String            # Display timing configuration
  isStatic: Boolean           # Static form flag
  isLightMode: Boolean        # Light theme flag
  displayPostAction: Boolean   # Post-submission action flag
  isDynamicAgreement: Boolean  # Dynamic agreement flag
  formFieldsCollection: [FormField]  # Form field configuration
  hiddenFieldsCollection: [HiddenField]  # Hidden field configuration
}
```

#### 2. FormField Content Type

```graphql
{
  altusFieldType: String       # Field type (Text, Email, Dropdown, etc.)
  altusFieldName: String       # Field name for form submission
  altusLabel: String           # Field label text
  altusIsRequired: Boolean     # Required field flag
  placeholderText: String      # Placeholder text
  validationErrorMessage: String  # Custom error message
  helperText: String           # Helper text
  isAltusEmailAllowed: Boolean # Allow Altus email domains
  isGenericEmailAllowed: Boolean # Allow generic email domains
  altusDataFormat: String      # Data format (Phone, etc.)
  sectionItemsCollection: [FormSection]  # Multi-step sections
}
```

### Form Configuration Strategy

- **Template Selection**: Choose appropriate form template for use case
- **Field Configuration**: Configure field types, validation, and styling
- **Integration Setup**: Configure submission endpoints and data mapping
- **Analytics Setup**: Configure tracking events and conversion goals

### Content Publishing Workflow

1. **Form Creation**: Create ComponentForm entry in Contentful
2. **Field Configuration**: Add and configure form fields
3. **Template Selection**: Choose appropriate form template
4. **Integration Setup**: Configure submission endpoints
5. **Preview Testing**: Test form in staging environment
6. **Publication**: Publish form to production
7. **Analytics Validation**: Verify tracking implementation

### Webhook Integration

- **Form Updates**: Real-time form configuration updates
- **Field Modifications**: Automatic form field reconfiguration
- **Template Changes**: Dynamic template switching
- **Analytics Events**: Form configuration change tracking

## Usage Instructions

### For Content Managers

#### 1. Creating New Forms

1. **Navigate to Contentful**: Access ComponentForm content type
2. **Create Form Entry**: Add new ComponentForm entry
3. **Select Template**: Choose appropriate form template
4. **Configure Fields**: Add form fields with validation rules
5. **Set Integration**: Configure submission endpoint
6. **Publish Form**: Make form live for users

#### 2. Form Field Configuration

```javascript
// Example form field configuration
{
  altusFieldType: "Email",
  altusFieldName: "email",
  altusLabel: "Email Address",
  altusIsRequired: true,
  placeholderText: "Enter your email",
  validationErrorMessage: "Please enter a valid email address",
  isAltusEmailAllowed: false,
  isGenericEmailAllowed: true
}
```

#### 3. Multi-Step Form Setup

```javascript
// Multi-step form section configuration
{
  altusFieldType: "Section",
  sectionItemsCollection: {
    items: [
      {
        altusFieldType: "Text",
        altusFieldName: "first_name",
        altusLabel: "First Name",
        altusIsRequired: true
      },
      {
        altusFieldType: "Email",
        altusFieldName: "email",
        altusLabel: "Email Address",
        altusIsRequired: true
      }
    ]
  }
}
```

### For Developers

#### 1. Adding New Form Template

```typescript
// 1. Create new form component
const NewFormTemplate = (props: FormI) => {
  // Form implementation
  return <div>New Form Template</div>
}

// 2. Add to FormRouter switch statement
case 'NewTemplate':
  FormProps = getFromsProps(props)
  return <NewFormTemplate {...FormProps} />
```

#### 2. Custom Field Type Implementation

```typescript
// Add new field type to FormFieldRouter
case 'CustomField':
  return (
    <div className="custom-field">
      <label htmlFor={randomId}>{props?.altusLabel}</label>
      <CustomInput
        id={randomId}
        name={props?.altusFieldName}
        required={props?.altusIsRequired}
        onChange={(value) => props?.onChange(value, props?.altusFieldName)}
      />
    </div>
  )
```

#### 3. Form Analytics Integration

```typescript
// Add custom form analytics event
export function customFormEvent(eventName: string, formId: string, data: any) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: `custom_form_${eventName}`,
      formId: formId,
      ...data,
      timestamp: new Date().toISOString(),
    })
  }
}
```

### For End Users

#### 1. Form Interaction

1. **Fill Required Fields**: Complete all required form fields
2. **Validation Feedback**: Address any validation errors
3. **Submit Form**: Click submit button to send data
4. **Confirmation**: Receive success confirmation message

#### 2. Multi-Step Forms

1. **Step Navigation**: Use Next/Previous buttons to navigate
2. **Progress Tracking**: Monitor progress through form steps
3. **Step Validation**: Complete required fields before proceeding
4. **Final Submission**: Submit completed form on final step

## Limitations

### Technical Limitations

1. **Form Complexity**: Maximum 20 fields per form for optimal performance
2. **File Uploads**: File upload fields not currently supported
3. **Real-time Validation**: 300ms debounce delay for email validation
4. **Browser Support**: IE11 not supported, modern browsers only
5. **Mobile Performance**: Limited functionality on older mobile devices

### Content Limitations

1. **Field Types**: Limited to predefined field types
2. **Validation Rules**: Custom validation patterns require development
3. **Form Length**: Multi-step forms limited to 10 steps maximum
4. **Integration Endpoints**: Limited to configured integration services
5. **Localization**: Manual translation required for form labels

### Performance Considerations

1. **Initial Load**: Form components loaded on demand
2. **Validation Processing**: Complex validation may have slight delays
3. **Submission Processing**: Form submission may take 2-3 seconds
4. **Memory Usage**: Multiple forms on page may increase memory usage
5. **API Rate Limits**: Form submissions subject to endpoint rate limits

## Third-party Dependencies

### Core Dependencies

1. **React Hook Form** (`react-hook-form`)

   - Purpose: Form state management and validation
   - Version: ^7.45.0
   - License: MIT
   - Critical: Yes

2. **Framer Motion** (`framer-motion`)

   - Purpose: Form animations and transitions
   - Version: ^11.11.17
   - License: MIT
   - Critical: No (graceful degradation)

3. **Bootstrap** (`bootstrap`)
   - Purpose: Form styling and validation classes
   - Version: ^5.3.0
   - License: MIT
   - Critical: Partially (styling only)

### Integration Dependencies

1. **Salesforce Marketing Cloud API**

   - Purpose: Marketing automation integration
   - Integration: REST API
   - Critical: For SFMC forms only

2. **Pardot API**

   - Purpose: B2B lead management
   - Integration: REST API
   - Critical: For Pardot forms only

3. **Reonomy API**
   - Purpose: Product trial registration
   - Integration: REST API
   - Critical: For Reonomy forms only

### Analytics Dependencies

1. **Google Analytics 4**

   - Purpose: Form interaction tracking
   - Integration: Custom events via dataLayer
   - Critical: For analytics only

2. **Microsoft Bing UET**
   - Purpose: Conversion tracking
   - Integration: JavaScript tracking
   - Critical: For Bing Ads only

## Considerations

### Performance Optimization

1. **Lazy Loading**: Load form components on demand
2. **Code Splitting**: Separate form bundles by template type
3. **Validation Debouncing**: Optimize validation timing (300ms)
4. **Caching Strategy**: Cache form configurations and validation rules
5. **Bundle Optimization**: Minimize form-related JavaScript bundles

### Accessibility Compliance

1. **ARIA Labels**: Comprehensive labeling for all form elements
2. **Keyboard Navigation**: Full keyboard accessibility for all interactions
3. **Screen Reader Support**: Optimized for NVDA, JAWS, VoiceOver
4. **Focus Management**: Proper focus handling in multi-step forms
5. **Color Contrast**: WCAG 2.1 AA compliant color schemes

### Security Considerations

1. **Input Sanitization**: Sanitize all form inputs before processing
2. **XSS Prevention**: Escape user-generated content
3. **API Security**: Secure integration endpoint communications
4. **Data Encryption**: Encrypt sensitive form data in transit
5. **Rate Limiting**: Implement form submission rate limiting

### SEO Optimization

1. **Form Accessibility**: Ensure forms are crawlable and accessible
2. **Structured Data**: Schema.org markup for form-related content
3. **Page Performance**: Optimize form loading for Core Web Vitals
4. **Mobile Optimization**: Mobile-first form design and functionality
5. **Error Handling**: SEO-friendly error pages and messaging

## Fall Back Plan

### Form Submission Fallback

1. **Primary Endpoint Failure**: Automatic failover to backup endpoints
2. **Client-side Storage**: Store form data locally if submission fails
3. **Retry Mechanism**: Automatic retry with exponential backoff
4. **Manual Submission**: Provide alternative submission methods
5. **Error Recovery**: Clear error messaging and recovery instructions

### Form Display Fallback

1. **JavaScript Disabled**: Basic HTML form functionality
2. **CSS Loading Failure**: Unstyled but functional forms
3. **Component Errors**: Error boundaries with fallback UI
4. **Network Issues**: Offline form data collection
5. **Browser Compatibility**: Progressive enhancement for older browsers

### Integration Service Failures

1. **SFMC Outage**:

   - Store submissions in local queue
   - Retry when service is restored
   - Alternative lead capture methods

2. **Pardot Failure**:

   - Fallback to direct email notifications
   - Manual lead processing workflows
   - Alternative CRM integration

3. **Analytics Failure**:
   - Local analytics storage
   - Alternative tracking methods
   - Manual conversion reporting

### Recovery Procedures

1. **Automated Monitoring**: Health checks every 60 seconds
2. **Alert System**: Immediate notifications for form failures
3. **Rollback Strategy**: Automated rollback to last known good state
4. **Manual Override**: Admin panel for emergency form management
5. **Communication Plan**: User notification system for extended outages

## Logic Flowchart

```mermaid
graph TD
    A[User Visits Page with Form] --> B[FormRouter Determines Template]
    B --> C{Form Template Type}

    C -->|Inline/Popup| D[Load Standard Form]
    C -->|Floating| E[Load Floating Form]
    C -->|MultiStep| F[Load Multi-Step Form]
    C -->|ExitIntent| G[Load Exit Intent Form]
    C -->|Hero| H[Load Hero Form]

    D --> I[Fetch Form Configuration]
    E --> I
    F --> I
    G --> I
    H --> I

    I --> J[Initialize Form State]
    J --> K[Render Form Fields]
    K --> L[User Interacts with Form]

    L --> M[Field Validation]
    M --> N{Validation Passed?}

    N -->|No| O[Show Validation Errors]
    N -->|Yes| P[Update Form State]

    O --> L
    P --> Q[User Submits Form]

    Q --> R[Final Validation]
    R --> S{All Fields Valid?}

    S -->|No| O
    S -->|Yes| T[Process Form Submission]

    T --> U[Send to Integration Endpoint]
    U --> V[Track Analytics Event]
    V --> W{Submission Successful?}

    W -->|Yes| X[Show Success Message]
    W -->|No| Y[Show Error Message]

    X --> Z[Form Complete]
    Y --> AA[Allow Retry]
    AA --> Q

    style A fill:#e1f5fe
    style X fill:#c8e6c9
    style Y fill:#ffcdd2
    style V fill:#fff3e0
```

## Form Validation Flow

```mermaid
graph LR
    A[User Input] --> B[Field Type Check]
    B --> C{Field Type}

    C -->|Email| D[Email Validation]
    C -->|Text| E[Text Validation]
    C -->|Dropdown| F[Selection Validation]
    C -->|Checkbox| G[Checkbox Validation]

    D --> H[Domain Check]
    E --> I[Required Check]
    F --> I
    G --> J[Array Processing]

    H --> K[Pattern Validation]
    I --> L[Length Validation]
    J --> M[Update Form State]

    K --> N{Validation Result}
    L --> N
    M --> N

    N -->|Valid| O[Clear Error State]
    N -->|Invalid| P[Set Error Message]

    O --> Q[Update UI State]
    P --> Q

    Q --> R[Enable/Disable Submit]

    style A fill:#e3f2fd
    style N fill:#f3e5f5
    style O fill:#e8f5e8
    style P fill:#ffebee
```

## Misc. Diagrams

### Form System Architecture

```mermaid
graph TB
    subgraph "Form System Architecture"
        A[FormRouter] --> B[Form Templates]
        A --> C[Form Field Router]
        A --> D[Form State Management]

        B --> E[Inline Forms]
        B --> F[Floating Forms]
        B --> G[Multi-Step Forms]
        B --> H[Exit Intent Forms]
        B --> I[Hero Forms]

        C --> J[Text Fields]
        C --> K[Email Fields]
        C --> L[Dropdown Fields]
        C --> M[Checkbox Fields]

        D --> N[Redux Form Slice]
        D --> O[Validation State]
        D --> P[Submission State]

        E --> Q[Form Submission]
        F --> Q
        G --> Q
        H --> Q
        I --> Q

        Q --> R[Integration APIs]
        Q --> S[Analytics Tracking]

        R --> T[SFMC]
        R --> U[Pardot]
        R --> V[Reonomy]

        S --> W[GA4 Events]
        S --> X[Conversion Tracking]
    end

    style A fill:#1976d2,color:#fff
    style D fill:#388e3c,color:#fff
    style Q fill:#f57c00,color:#fff
    style S fill:#d32f2f,color:#fff
```

### Form Submission Data Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Form Component
    participant V as Validation Engine
    participant S as Redux Store
    participant A as Analytics
    participant I as Integration API

    U->>F: Fill Form Fields
    F->>V: Validate Input
    V->>F: Return Validation Result
    F->>S: Update Form State

    U->>F: Submit Form
    F->>V: Final Validation
    V->>F: Validation Complete

    F->>S: Set Loading State
    F->>A: Track Form Submission
    F->>I: Send Form Data

    I->>F: Return Success/Error
    F->>S: Update Submission State
    F->>A: Track Submission Result
    F->>U: Show Result Message
```

## Misc. Info

### Browser Compatibility

- **Chrome**: 90+ (Full support)
- **Firefox**: 88+ (Full support)
- **Safari**: 14+ (Full support)
- **Edge**: 90+ (Full support)
- **Mobile Safari**: 14+ (Touch-optimized)
- **Chrome Mobile**: 90+ (Touch-optimized)

### Performance Benchmarks

- **Form Load**: <1 second (initial render)
- **Field Validation**: <300ms (debounced)
- **Form Submission**: <3 seconds (including API calls)
- **Memory Usage**: <20MB (typical form usage)
- **Bundle Size**: ~1.8MB (all form components)

### Form Analytics Events

- **Form Start**: `custom_form_start`
- **Form Success**: `custom_form_success`
- **Form Error**: `custom_form_error`
- **Form Close**: `custom_form_floating_close`
- **Field Validation**: `form_field_validation_error`
- **Step Navigation**: `form_step_navigation`

### Content Guidelines

1. **Form Labels**: Use clear, descriptive field labels
2. **Error Messages**: Provide specific, actionable error messages
3. **Success Messages**: Confirm successful submission with next steps
4. **Helper Text**: Include helpful guidance for complex fields
5. **Accessibility**: Provide alt text and ARIA labels for all elements

## APIs

### Contentful GraphQL API

#### ComponentForm Query

```graphql
query ComponentForm($id: String!, $locale: String!) {
  componentForm(id: $id, locale: $locale) {
    template
    endpointUrl
    sfmcUrl
    formCategory
    header {
      json
    }
    footer {
      json
    }
    thankYouMessage {
      json
    }
    loadingMessage {
      json
    }
    successMessage {
      json
    }
    errorMessage {
      json
    }
    backgroundImage {
      url
      title
      description
    }
    displayAt
    isStatic
    isLightMode
    displayPostAction
    isDynamicAgreement
    formFieldsCollection(limit: 20) {
      items {
        altusFieldType
        altusFieldName
        altusLabel
        altusIsRequired
        placeholderText
        validationErrorMessage
        helperText
        isAltusEmailAllowed
        isGenericEmailAllowed
        altusDataFormat
        sectionItemsCollection {
          items {
            altusFieldType
            altusFieldName
            altusLabel
            altusIsRequired
          }
        }
      }
    }
    hiddenFieldsCollection {
      items {
        formField {
          altusFieldName
          altusFieldType
        }
      }
    }
    sys {
      id
    }
  }
}
```

### Form Integration APIs

#### SFMC Integration

```javascript
// SFMC form submission
const sfmcSubmission = {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: new URLSearchParams({
    Email: formData.email,
    firstname: formData.first_name,
    lastname: formData.last_name,
    company: formData.company,
    utm_source: formData.utm_source,
    utm_campaign: formData.utm_campaign,
    formid: formData.formid,
  }),
}
```

#### Pardot Integration

```javascript
// Pardot form submission
const pardotSubmission = {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: new URLSearchParams({
    email: formData.email,
    first_name: formData.first_name,
    last_name: formData.last_name,
    company: formData.company,
    campaign_id: formData.campaign_id,
    source: formData.sourceUrl,
  }),
}
```

#### Reonomy Integration

```javascript
// Reonomy trial registration
const reonomySubmission = {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: formData.email,
    first_name: formData.first_name,
    last_name: formData.last_name,
    password: formData.password,
    phone: formData.phone,
    accept_agreements: ['466c3907-63ee-49fd-b2b2-a225c5765462'],
  }),
}
```

### Google Analytics 4 API

#### Form Event Tracking

```javascript
// Form Success Event
{
  event: 'custom_form_success',
  formId: 'contentful-form-id',
  formCategory: 'lead-generation',
  contactInquiryType: 'demo-request',
  productInterest: 'commercial-real-estate',
  timestamp: '2024-01-15T10:30:00Z'
}

// Form Error Event
{
  event: 'custom_form_error',
  formId: 'contentful-form-id',
  formCategory: 'newsletter-signup',
  errorType: 'validation-error',
  errorField: 'email',
  timestamp: '2024-01-15T10:31:00Z'
}

// Form Start Event
{
  event: 'custom_form_start',
  formId: 'contentful-form-id',
  formTemplate: 'floating',
  pageUrl: '/insights/market-trends',
  timestamp: '2024-01-15T10:29:00Z'
}
```

## Credentials

### Environment Variables Required

#### Production Environment

```bash
# Form Integration Endpoints
SFMC_ENDPOINT_URL=https://cloud.mc.exacttarget.com/your-endpoint
PARDOT_ENDPOINT_URL=https://go.pardot.com/your-endpoint
REONOMY_API_ENDPOINT=https://api.reonomy.com/v1

# API Authentication
SFMC_API_KEY=your_sfmc_api_key
PARDOT_API_KEY=your_pardot_api_key
REONOMY_API_KEY=your_reonomy_api_key

# Analytics Configuration
NEXT_PUBLIC_GA4_MEASUREMENT_ID=G-XXXXXXXXXX
GA4_API_SECRET=your_ga4_api_secret
UET_TAG_ID=your_bing_uet_tag_id

# Contentful Configuration
CONTENTFUL_SPACE_ID=your_production_space_id
CONTENTFUL_ACCESS_TOKEN=your_production_access_token
CONTENTFUL_PREVIEW_ACCESS_TOKEN=your_preview_token

# Security Configuration
FORM_ENCRYPTION_KEY=your_32_character_encryption_key
API_RATE_LIMIT_PER_MINUTE=100
CORS_ALLOWED_ORIGINS=https://altusgroup.com,https://www.altusgroup.com

# Application Configuration
NEXT_PUBLIC_DOMAIN=altusgroup.com
NEXT_PUBLIC_LOG=false
NODE_ENV=production
```

#### Development Environment

```bash
# Form Integration Endpoints (Staging)
SFMC_ENDPOINT_URL=https://staging-cloud.mc.exacttarget.com/your-endpoint
PARDOT_ENDPOINT_URL=https://staging-go.pardot.com/your-endpoint
REONOMY_API_ENDPOINT=https://staging-api.reonomy.com/v1

# Development Overrides
NEXT_PUBLIC_LOG=true
NODE_ENV=development
FORM_DEBUG_MODE=true
```

### API Key Management

1. **SFMC Keys**: Managed through Salesforce Marketing Cloud dashboard
2. **Pardot Keys**: Configured through Pardot administration panel
3. **Reonomy Keys**: Managed through Reonomy developer portal
4. **GA4 Credentials**: Configured through Google Analytics dashboard
5. **Contentful Keys**: Managed through Contentful space settings

### Security Best Practices

1. **Key Rotation**: Monthly rotation of API keys
2. **Access Restrictions**: IP-based restrictions for API endpoints
3. **Environment Separation**: Separate keys for dev/staging/production
4. **Monitoring**: API usage monitoring and alerting
5. **Encryption**: All form data encrypted in transit and at rest

---

_This documentation is maintained by the Altus Group development team. For updates or questions, please contact the development team or create an issue in the GitHub repository._
