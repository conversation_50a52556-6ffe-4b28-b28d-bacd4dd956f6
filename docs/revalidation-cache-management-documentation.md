# Revalidation and Cache Management Documentation

## Overview

This document provides a comprehensive overview of the revalidation and cache management strategies implemented in the 17.studio Next.js project. The system employs multiple cache invalidation techniques including ISR (Incremental Static Regeneration), tag-based revalidation, path-based revalidation, and webhook-driven cache flushing.

## Architecture

### Core Cache Management Components

1. **ISR (Incremental Static Regeneration)** - Time-based automatic revalidation
2. **Tag-based Revalidation** - Content-specific cache invalidation
3. **Path-based Revalidation** - URL-specific cache clearing
4. **Webhook Integration** - CMS-triggered cache updates
5. **Manual Cache Flushing** - Developer tools for cache management
6. **Cross-Domain Revalidation** - Multi-site cache coordination

## ISR (Incremental Static Regeneration)

### Configuration

**Location**: `src/app/[[...slug]]/page.tsx`

```typescript
export const revalidate = 3600 // seconds (1 hour)
```

### Automated Revalidation Time Management

**Location**: `automation/updateRevalidateTime.cjs`

```javascript
const newRevalidateTime = 3600 // Set desired revalidation time in seconds

// Automatically updates revalidate time in page.tsx during build
const regex = /(export const revalidate = )(\d+)/
const updatedData = data.replace(regex, `$1${newRevalidateTime}`)
```

### Build Process Integration

**Location**: `package.json`

```json
{
  "scripts": {
    "prebuild": "yarn move-files && node automation/updateRevalidateTime.cjs",
    "build": "yarn prebuild && next build && node src/algolia/algolia-sync.js"
  }
}
```

## API Routes for Cache Management

### 1. Tag-based Revalidation

**Endpoint**: `POST /api/revalidate`
**Location**: `src/app/api/revalidate/route.ts`

```typescript
export async function POST(request: NextRequest) {
  const req = await request.json()
  const tag = req?.tag

  if (tag) {
    console.log('POST: revalidate tag', tag)
    revalidateTag(tag)
    return Response.json({ revalidated: true, now: Date.now(), tag })
  }

  return Response.json({
    revalidated: false,
    now: Date.now(),
    message: 'Missing tag to revalidate',
  })
}
```

**Usage**:

```bash
curl -X POST /api/revalidate \
  -H "Content-Type: application/json" \
  -d '{"tag": "your-cache-tag"}'
```

### 2. Path-based Revalidation

**Endpoint**: `POST /api/revalidate/page`
**Location**: `src/app/api/revalidate/page/route.ts`

```typescript
export async function POST(request: NextRequest) {
  const req = await request.json()
  let slugs = req?.slugs

  if (slugs?.length > 0) {
    slugs.forEach((slug: string) => {
      revalidatePath(slug)
    })
    return Response.json({ revalidated: true, now: Date.now(), req })
  }

  return Response.json({
    revalidated: false,
    now: Date.now(),
    message: 'Missing slug to revalidate',
  })
}
```

**Features**:

- CORS support for cross-origin requests
- Batch revalidation of multiple paths
- Comprehensive error handling

**Usage**:

```bash
curl -X POST /api/revalidate/page \
  -H "Content-Type: application/json" \
  -d '{"slugs": ["/page1", "/page2/", "/page3"]}'
```

### 3. Full Site Revalidation

**Endpoint**: `GET /api/revalidate/full?key=sf9ds8fd9fgdsf8dgk`
**Location**: `src/app/api/revalidate/full/route.ts`

```typescript
export async function GET(request: NextRequest) {
  const key = request.nextUrl.searchParams.get('key')

  if (key === 'sf9ds8fd9fgdsf8dgk') {
    revalidatePath('/', 'layout')
    return Response.json({
      revalidated: true,
      now: Date.now(),
      message: 'All paths revalidated.',
    })
  }
}
```

**Security**: Protected by secret key authentication

### 4. Webhook-based Revalidation

**Endpoint**: `POST /api/revalidate/page-webhook`
**Location**: `src/app/api/revalidate/page-webhook/route.ts`

```typescript
/**
 * Expected JSON structure:
 * {
 *   fields: {
 *     slug: {
 *       [locale]: string
 *     }
 *   }
 * }
 */
export async function POST(request: NextRequest) {
  const body = await request.json()
  const slugObject = body?.fields?.slug

  // Extract slug values and build possible URL formats
  const slugValues: string[] = Object.values(slugObject)
  const allSlugs = slugValues.flatMap((slug) => [`/${slug}/`, `/${slug}`])

  // Revalidate each path
  for (const path of allSlugs) {
    revalidatePath(path)
  }
}
```

**Integration**: Designed for Contentful webhook integration

## AFS (Advanced Filtering System) Revalidation

### Cross-Domain AFS Cache Management

**Endpoint**: `POST /api/revalidate/afs`
**Location**: `src/app/api/revalidate/afs/route.ts`

```typescript
export async function POST(request: NextRequest) {
  const req = await request.json()

  const domainTag = req.metadata.tags.find((tag: cfTag) =>
    tag.sys.id.startsWith('domain')
  )?.sys?.id

  const afsTag = req.metadata.tags.find((tag: cfTag) =>
    tag.sys.id.startsWith('afs')
  )?.sys?.id

  const domainVercelUrl = getVercelUrl(domainTag)
  const afsSlug = getAFSslugFromTag(afsTag, domainTag)

  // Cross-domain revalidation
  const revalidateResponse = await fetch(
    `${domainVercelUrl}/api/revalidate/page/`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ slugs: afsSlug }),
    }
  )
}
```

### Domain-Specific AFS Slug Mapping

```typescript
function getAFSslugFromTag(afsTag: string, domainTag: string): string[] {
  switch (domainTag) {
    case 'domainAltusGroupCom':
      const tagToslug: Record<string, string[]> = {
        afsInsights: [
          '/insights/all',
          '/insights/all/',
          '/insights/all/fr',
          '/insights/all/fr/',
        ],
        afsPressRelease: [
          '/press-releases',
          '/press-releases/',
          '/press-releases/fr',
          '/press-releases/fr/',
        ],
        afsEvents: ['/events', '/events/', '/events/fr', '/events/fr/'],
      }
      return tagToslug[afsTag]

    case 'domainFinanceActiveCom':
      return [
        '/resources',
        '/resources/',
        '/fr/ressources',
        '/fr/ressources/',
        '/de/ressourcen',
        '/de/ressourcen/',
        '/it/risorse',
        '/it/risorse/',
        '/es/recursos',
        '/es/recursos/',
        '/nl/media',
        '/nl/media/',
      ]

    default:
      return []
  }
}
```

### Cross-Domain URL Mapping

```typescript
function getVercelUrl(domainTag: string) {
  const domainToUrl: Record<string, string> = {
    domainAltusGroupCom: 'https://msa-agl-v3w-git-staging-altus.vercel.app',
    domainReonomyCom: 'https://msa-reo-v3w-git-staging-altus.vercel.app',
    domainFinanceActiveCom: 'https://msa-fia-v3w-git-staging-altus.vercel.app',
  }
  return domainToUrl[domainTag]
}
```

## Cache Tagging Strategy

### GraphQL Fetch with Cache Tags

**Location**: `src/lib/api.ts` & `confighelper.js`

```typescript
export async function fetchGraphQL(
  query: string,
  preview = false,
  revalidateTag?: string,
  retries = 5
): Promise<unknown> {
  let nextConfig = {}
  if (revalidateTag) {
    nextConfig = { tags: [revalidateTag] }
  }

  const response = await fetch(contentfulEndpoint, {
    next: nextConfig,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${contentfulAccessToken}`,
    },
    body: JSON.stringify({ query }),
  })
}
```

### Cache Tag Usage Examples

```typescript
// Tag-specific cache invalidation
await fetchGraphQL(query, false, 'experimentation')
await fetchGraphQL(query, false, 'navigation')
await fetchGraphQL(query, false, 'page-content')
```

## Manual Cache Flushing Tools

### Development Flush Button

**Location**: `src/utils/FlushPageFloatingButton.tsx`

```typescript
const handleFlushCache = async () => {
  try {
    const url = window.location.href
    const parsedUrl = new URL(url)
    const vercelUrl = `${parsedUrl.protocol}//${parsedUrl.host}`
    const pathname = parsedUrl.pathname.replace(/\/$/, '')
    const lang = parsedUrl.searchParams.get('lang')

    // Determine slugs dynamically
    const slugs = lang
      ? [
          `${pathname}`,
          `${pathname}/${lang}`,
          `${pathname}/`,
          `${pathname}/${lang}/`,
        ]
      : [`${pathname}`, `${pathname}/`]

    // API call to flush cache
    const response = await fetch(`${vercelUrl}/api/revalidate/page/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ slugs }),
    })
  } catch (error) {
    console.error('Cache flush failed:', error)
  }
}
```

**Features**:

- Only visible in non-production environments
- Automatic slug detection with language variants
- Real-time cache flushing for current page

### Integration in Page Components

**Location**: `src/utils/PageRoot.tsx`

```typescript
const PageJSX = () => {
  return (
    <ScrollIdProvider response={pageResponse} allCompDataArray={sortedComponents}>
      {process.env.VERCEL_ENV !== 'production' && <FlushPageFloatingButton />}
      {/* Rest of page content */}
    </ScrollIdProvider>
  )
}
```

## Webhook Integration

### Contentful Webhook Configuration

The system supports multiple webhook endpoints for different content types:

1. **Page Content Updates**: `/api/revalidate/page-webhook`
2. **AFS Content Updates**: `/api/revalidate/afs`
3. **General Tag Updates**: `/api/revalidate`

### Webhook Payload Processing

```typescript
// Page webhook payload structure
{
  "fields": {
    "slug": {
      "en-CA": "page-slug",
      "fr": "page-slug-fr"
    }
  }
}

// AFS webhook payload structure
{
  "metadata": {
    "tags": [
      { "sys": { "id": "domainAltusGroupCom" } },
      { "sys": { "id": "afsInsights" } }
    ]
  }
}
```

## Error Handling and Monitoring

### Comprehensive Error Responses

```typescript
// Standardized error response format
return Response.json({
  revalidated: false,
  now: Date.now(),
  message: 'Descriptive error message',
  requestBody: body, // For debugging
  error: 'Technical error details',
})
```

### Logging and Debugging

```typescript
// Detailed logging for cache operations
console.log('POST: revalidate tag', tag)
console.log('POST: revalidate afs', domainTag, afsTag)
console.log('POST: req body', req)
```

### Retry Mechanisms

```typescript
// GraphQL fetch with retry logic
if (responseData.errors?.[0]?.message.includes('rate limit')) {
  if (retries > 0) {
    console.error('Retry counter: ', 5 - retries, ' API::error', responseData)
    await new Promise((resolve) => setTimeout(resolve, 500))
    responseData = await fetchGraphQL(
      query,
      preview,
      revalidateTag,
      retries - 1
    )
  } else {
    throw new Error('API::error: retries exceeded')
  }
}
```

## Cache Strategies and Performance

### Multi-Level Caching Architecture

1. **Next.js App Router Cache**: Automatic caching of route segments
2. **Data Cache**: Fetch requests cached with tags for selective invalidation
3. **Full Route Cache**: Static generation with ISR for dynamic updates
4. **Router Cache**: Client-side navigation cache

### Cache Invalidation Patterns

#### 1. Content-Driven Invalidation

- **Trigger**: Content updates in Contentful CMS
- **Method**: Webhook → API route → `revalidateTag()` or `revalidatePath()`
- **Scope**: Specific content types or pages

#### 2. Time-Based Invalidation

- **Trigger**: ISR timer expiration (3600 seconds)
- **Method**: Automatic Next.js revalidation
- **Scope**: All static pages

#### 3. Manual Invalidation

- **Trigger**: Developer action or deployment
- **Method**: Direct API calls or flush button
- **Scope**: Configurable (single page, tag, or full site)

#### 4. Cross-Domain Invalidation

- **Trigger**: AFS content updates affecting multiple domains
- **Method**: Cross-domain API calls
- **Scope**: Related pages across business domains

### Performance Optimizations

#### 1. Selective Cache Invalidation

```typescript
// Only invalidate specific content types
revalidateTag('navigation') // Only navigation components
revalidateTag('afsInsights') // Only AFS insights pages
revalidateTag('experimentation') // Only A/B test variants
```

#### 2. Batch Revalidation

```typescript
// Revalidate multiple related paths together
const slugs = ['/page', '/page/', '/page/fr', '/page/fr/']
slugs.forEach((slug: string) => {
  revalidatePath(slug)
})
```

#### 3. Intelligent Slug Generation

```typescript
// Automatic generation of all possible URL variants
const allSlugs = slugValues.flatMap((slug) => [`/${slug}/`, `/${slug}`])
```

## Security and Access Control

### API Route Protection

#### 1. Secret Key Authentication

```typescript
// Full site revalidation requires secret key
if (key === 'sf9ds8fd9fgdsf8dgk') {
  revalidatePath('/', 'layout')
}
```

#### 2. CORS Configuration

```typescript
// Controlled cross-origin access
const corsAllowedOrigins = ['https://app.contentful.com']
const corsAllowedPattern = /^https:\/\/.*\.vercel\.app$/

if (corsAllowedPattern.test(origin) || corsAllowedOrigins.includes(origin)) {
  response.headers.append('Access-Control-Allow-Origin', origin)
}
```

#### 3. Environment-Based Access

```typescript
// Development tools only in non-production
{process.env.VERCEL_ENV !== 'production' && <FlushPageFloatingButton />}
```

### Webhook Security

#### 1. Payload Validation

```typescript
// Validate webhook payload structure
if (!slugObject || typeof slugObject !== 'object') {
  return Response.json({
    revalidated: false,
    message: 'Invalid or missing slug data',
    requestBody: body,
  })
}
```

#### 2. Content Filtering

```typescript
// Only process valid domain and AFS tags
const domainTag = req.metadata.tags.find((tag: cfTag) =>
  tag.sys.id.startsWith('domain')
)?.sys?.id

const afsTag = req.metadata.tags.find((tag: cfTag) =>
  tag.sys.id.startsWith('afs')
)?.sys?.id
```

## Monitoring and Analytics

### Cache Performance Tracking

#### 1. Response Time Monitoring

```typescript
// Track revalidation response times
return Response.json({
  revalidated: true,
  now: Date.now(),
  processingTime: Date.now() - startTime,
  tag,
})
```

#### 2. Error Rate Tracking

```typescript
// Comprehensive error logging
console.error('Error during revalidation:', error)
return Response.json({
  revalidated: false,
  now: Date.now(),
  error: 'Failed to parse request or revalidate paths',
  requestBody: request?.body,
})
```

#### 3. Cache Hit/Miss Analytics

- Integration with Vercel Analytics
- Custom logging for cache effectiveness
- Performance metrics collection

### Debugging Tools

#### 1. Development Logging

```typescript
// Conditional logging based on environment
if (process.env.NEXT_PUBLIC_LOG === 'true') {
  console.log(...args, '\n\n++++++++++++ CL Function Log ++++++++++++++++')
}
```

#### 2. Cache State Inspection

```typescript
// Visual cache flush feedback
const [isLoading, setIsLoading] = useState(false)
const [lastFlushed, setLastFlushed] = useState<Date | null>(null)

const handleFlushCache = async () => {
  setIsLoading(true)
  try {
    await flushCache()
    setLastFlushed(new Date())
  } finally {
    setIsLoading(false)
  }
}
```

## Best Practices and Recommendations

### 1. Cache Tag Naming Convention

- Use descriptive, hierarchical tags: `domain:content-type:specific-id`
- Examples: `agl:navigation:header`, `fia:insights:category`

### 2. Revalidation Frequency

- **High-frequency content**: Use webhook-driven revalidation
- **Static content**: Rely on ISR with longer intervals (3600s)
- **Critical updates**: Implement immediate manual revalidation

### 3. Error Handling

- Always provide meaningful error messages
- Include request context for debugging
- Implement graceful fallbacks for cache failures

### 4. Performance Considerations

- Batch related revalidations together
- Use specific tags rather than broad path revalidation
- Monitor cache effectiveness and adjust strategies

### 5. Development Workflow

- Use flush button for immediate testing
- Implement staging environment cache testing
- Document cache dependencies for team awareness

## Troubleshooting Common Issues

### 1. Cache Not Invalidating

- **Check**: Webhook configuration and payload format
- **Verify**: API route accessibility and CORS settings
- **Test**: Manual revalidation via direct API calls

### 2. Performance Degradation

- **Monitor**: Revalidation frequency and batch sizes
- **Optimize**: Use more specific cache tags
- **Review**: ISR timing configuration

### 3. Cross-Domain Issues

- **Validate**: Domain URL mappings in AFS configuration
- **Check**: Network connectivity between domains
- **Verify**: Authentication and CORS headers

### 4. Development Environment Problems

- **Ensure**: Proper environment variable configuration
- **Check**: Development tool visibility settings
- **Verify**: Local vs. production behavior differences

## Maintenance and Updates

### Regular Maintenance Tasks

1. **Review cache hit rates** and adjust strategies
2. **Monitor webhook reliability** and error rates
3. **Update domain mappings** for new business units
4. **Audit cache tag usage** for optimization opportunities

### Version Updates

- Test cache behavior after Next.js updates
- Verify webhook compatibility with CMS changes
- Update documentation for new cache features

### Performance Audits

- Regular analysis of cache effectiveness
- Identification of over-cached or under-cached content
- Optimization of revalidation patterns based on usage data
