# Forms System (FormRouter) Documentation

## Description / Overview

The Forms System is a comprehensive form management solution built for the Altus Group website, providing dynamic form creation, validation, submission, and analytics tracking across multiple form templates and use cases. The system features intelligent routing, multi-step forms, exit-intent popups, floating forms, and seamless integration with marketing automation platforms including SFMC (Salesforce Marketing Cloud), Pardot, and Reonomy.

### Key Features

- **Multi-Template Support**: 9 different form templates (Inline, Popup, Floating, Multi-Step, Hero, Exit Intent, etc.)
- **Advanced Validation**: Real-time email validation, custom field validation, and error handling
- **Marketing Integration**: SFMC, Pardot, and Reonomy API integrations
- **Analytics Tracking**: Comprehensive GA4 event tracking and conversion monitoring
- **Responsive Design**: Mobile-optimized forms with touch-friendly interfaces
- **Accessibility Compliance**: WCAG 2.1 AA compliant with full keyboard navigation
- **Exit Intent Detection**: Smart popup forms triggered by user behavior
- **Multi-Step Forms**: Progressive form completion with step validation
- **Real-time Feedback**: Toast notifications, loading states, and success messages

## Contributors

### Development Team

- **Frontend Engineers**: React/Next.js form specialists
- **Backend Engineers**: API integration and data processing specialists
- **UX/UI Designers**: Form interface and user experience designers
- **Analytics Engineers**: GA4 and conversion tracking implementation
- **Marketing Engineers**: SFMC and marketing automation integration
- **DevOps Engineers**: Form submission infrastructure and monitoring

### Stakeholders

- **Marketing Team**: Lead generation and campaign tracking requirements
- **Sales Team**: Lead qualification and CRM integration needs
- **Content Managers**: Form configuration and content management
- **Product Managers**: Form conversion optimization and user experience
- **Compliance Team**: Data privacy and accessibility requirements

## Functionality

### Core Components

#### 1. FormRouter (`FormRouter.tsx`)

Central routing component that determines which form template to render based on configuration.

**Supported Templates:**

- `Inline` - Standard inline forms embedded in content
- `Popup` - Modal popup forms triggered by user actions
- `Floating` - Floating newsletter subscription forms
- `Floating - Home` - Homepage-specific floating forms
- `FloatingComponent` - Insights page floating forms
- `ReonomyFreeTrial` - Reonomy product trial registration
- `MultiStep` - Multi-step form wizard with progress tracking
- `FormHero` - Hero section forms with prominent placement
- `ExitIntent` - Exit-intent popup forms for lead capture

#### 2. Form Templates

##### Inline Forms (`Form/index.tsx`)

- **Purpose**: Standard forms embedded within page content
- **Features**: Full field validation, custom styling, responsive design
- **Use Cases**: Contact forms, demo requests, newsletter signups
- **Analytics**: Form start, completion, and error tracking

##### Popup Forms

- **Purpose**: Modal forms triggered by user interactions
- **Features**: Overlay display, close functionality, focus management
- **Use Cases**: Lead magnets, content gating, promotional offers
- **Integration**: Same core functionality as inline forms

##### Floating Forms (`FormFloating/index.tsx`)

- **Purpose**: Persistent newsletter subscription forms
- **Features**: Minimalist design, session storage integration, auto-hide on success
- **Use Cases**: Newsletter subscriptions, content updates, blog notifications
- **Behavior**: Stores subscription status to prevent re-appearance

##### Multi-Step Forms (`FormMultiStep/index.tsx`)

- **Purpose**: Complex forms broken into manageable steps
- **Features**: Progress indicators, step validation, data persistence
- **Use Cases**: Product trials, detailed registrations, onboarding flows
- **Navigation**: Forward/backward navigation with validation checkpoints

##### Exit Intent Forms (`FormExitIntent/index.tsx`)

- **Purpose**: Capture leads when users attempt to leave the page
- **Features**: Mouse movement detection, browser back button detection, session tracking
- **Use Cases**: Last-chance offers, newsletter signups, feedback collection
- **Triggers**: Mouse leave events, browser navigation attempts, inactivity timers

##### Hero Forms (`FormHero/index.tsx`)

- **Purpose**: Prominent forms in hero sections
- **Features**: Horizontal layout, prominent CTAs, streamlined design
- **Use Cases**: Landing page conversions, product demos, trial signups
- **Design**: Optimized for high visibility and conversion

#### 3. Form Field System

##### Supported Field Types

- **Text Fields**: Standard text input with validation
- **Email Fields**: Email validation with domain restrictions
- **Phone Fields**: Numeric input with formatting
- **Dropdown Fields**: Single and multi-select options
- **Checkbox Fields**: Multiple selection with array handling
- **Hidden Fields**: UTM parameters and tracking data
- **Textarea Fields**: Multi-line text input
- **Radio Fields**: Single selection from options

##### Field Configuration

```typescript
interface FormField {
  altusFieldType: 'Text' | 'Email' | 'Dropdown' | 'Checkbox' | 'Hidden'
  altusFieldName: string
  altusLabel: string
  altusIsRequired: boolean
  placeholderText: string
  validationErrorMessage: string
  helperText: string
  isAltusEmailAllowed: boolean
  isGenericEmailAllowed: boolean
}
```

### Form Validation System

#### 1. Email Validation

- **Domain Restrictions**: Block/allow specific email domains
- **Generic Email Handling**: Control generic email providers
- **Real-time Validation**: Debounced validation during typing
- **Custom Error Messages**: Configurable validation messages

#### 2. Required Field Validation

- **Client-side Validation**: Immediate feedback on required fields
- **Form Submission Blocking**: Prevent submission with validation errors
- **Visual Indicators**: Error styling and messaging
- **Accessibility**: Screen reader compatible error announcements

#### 3. Custom Validation Patterns

- **Phone Number Formatting**: Automatic formatting and validation
- **Text Length Limits**: Minimum and maximum character validation
- **Pattern Matching**: Regex-based validation for specific formats
- **Cross-field Validation**: Dependent field validation logic

### Form Submission Processing

#### 1. Data Collection and Processing

```typescript
interface FormSubmissionData {
  // User-entered data
  email: string
  first_name: string
  last_name: string
  company: string

  // Tracking data
  utm_campaign: string
  utm_source: string
  utm_medium: string
  sourceUrl: string
  formid: string
  clientid: string
  measurementid: string
}
```

#### 2. Integration Endpoints

##### SFMC (Salesforce Marketing Cloud) Integration

- **Purpose**: Marketing automation and lead nurturing
- **Data Mapping**: Form fields to SFMC data extensions
- **Tracking**: Campaign attribution and lead scoring
- **Error Handling**: Retry logic and fallback mechanisms

##### Pardot Integration

- **Purpose**: B2B lead management and scoring
- **Features**: Lead qualification, campaign tracking, ROI measurement
- **Data Processing**: Form data transformation for Pardot API
- **Analytics**: Conversion tracking and attribution

##### Reonomy Integration

- **Purpose**: Product trial registration and user management
- **Features**: Account creation, trial activation, user onboarding
- **Security**: Secure API communication and data handling
- **Validation**: Enhanced validation for trial registrations

## UI Components

### Form Layout Components

#### 1. Form Container

- **Purpose**: Consistent form styling and layout
- **Features**: Responsive design, shadow effects, padding management
- **Styling**: SCSS modules with theme support
- **Accessibility**: Proper form landmarks and structure

#### 2. Input Components

- **Text Inputs**: Styled text fields with validation states
- **Dropdown Selects**: Custom-styled select components
- **Checkbox Groups**: Multi-select checkbox arrays
- **Button Components**: Form submission and navigation buttons

#### 3. Feedback Components

- **Toast Notifications**: Success, error, and loading messages
- **Progress Indicators**: Multi-step form progress tracking
- **Validation Messages**: Inline error and helper text
- **Loading States**: Form submission progress indicators

### Responsive Design Features

#### 1. Mobile Optimization

- **Touch-friendly Inputs**: Larger touch targets for mobile devices
- **Responsive Layouts**: Adaptive form layouts for different screen sizes
- **Mobile-specific Behaviors**: Keyboard optimization, focus management
- **Performance**: Optimized for mobile network conditions

#### 2. Accessibility Features

- **ARIA Labels**: Comprehensive labeling for screen readers
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Proper focus handling and visual indicators
- **Color Contrast**: WCAG 2.1 AA compliant color schemes
- **Screen Reader Support**: Optimized for assistive technologies

## Codebase Integration

### File Structure

```
src/
├── lib/componentsRouter/
│   ├── FormRouter.tsx                # Main form routing component
│   └── FormFieldRouter.tsx           # Form field routing component
├── systems/AltusForms/
│   ├── Form/                         # Core form component
│   │   ├── index.tsx
│   │   ├── interface.ts
│   │   ├── defaults.tsx
│   │   ├── utils.tsx
│   │   ├── inputType.tsx
│   │   └── @core/
│   │       ├── EmailField.tsx
│   │       └── AllFormsRender.tsx
│   ├── FormFloating/                 # Floating form components
│   ├── FormFloatingHome/             # Homepage floating forms
│   ├── FormFloatingInsights/         # Insights page forms
│   ├── FormMultiStep/                # Multi-step form wizard
│   ├── FormHero/                     # Hero section forms
│   └── FormExitIntent/               # Exit intent popup forms
├── redux/slices/
│   ├── formSlice.ts                  # Form state management
│   └── popupSlice.ts                 # Popup state management
├── lib/propsMapping/
│   └── form.mapping.ts               # Form props transformation
├── lib/queries/
│   └── form.query.ts                 # GraphQL form queries
└── utils/
    └── analyticsEvents.ts            # Form analytics tracking
```

### Key Dependencies

- **React 18+**: Component framework with hooks
- **Next.js 14**: Application framework and routing
- **Redux Toolkit**: Form state management
- **Contentful**: Headless CMS for form configuration
- **Framer Motion**: Form animations and transitions
- **Bootstrap**: Form styling and validation classes
- **Moment.js**: Date handling and formatting

### Integration Points

#### 1. Contentful CMS Integration

- **Content Types**: ComponentForm for form configuration
- **Field Management**: Dynamic form field configuration
- **Template Selection**: Form template and styling options
- **Content Delivery**: Real-time form updates via API

#### 2. Redux State Management

- **Form Slice**: Form values, validation state, submission status
- **Popup Slice**: Exit intent and modal form state
- **Session Management**: Form activation and completion tracking
- **Data Persistence**: Form data preservation across sessions

#### 3. Analytics Integration

- **GA4 Events**: Form interaction and conversion tracking
- **Custom Events**: Form-specific analytics events
- **Conversion Tracking**: Lead generation and ROI measurement
- **User Behavior**: Form abandonment and completion analysis

## Vercel Deployment

### Environment Configuration

```bash
# Form Integration Endpoints
SFMC_ENDPOINT_URL=https://your-sfmc-endpoint.com
PARDOT_ENDPOINT_URL=https://your-pardot-endpoint.com
REONOMY_API_ENDPOINT=https://api.reonomy.com

# Analytics Configuration
NEXT_PUBLIC_GA4_MEASUREMENT_ID=G-XXXXXXXXXX
GA4_API_SECRET=your_ga4_api_secret

# Contentful Configuration
CONTENTFUL_SPACE_ID=your_space_id
CONTENTFUL_ACCESS_TOKEN=your_access_token

# Security Configuration
FORM_ENCRYPTION_KEY=your_encryption_key
API_RATE_LIMIT=100
```

### Build Configuration

- **Node.js Version**: 18.x
- **Build Command**: `yarn build`
- **Form Validation**: Build-time form configuration validation
- **Asset Optimization**: Form-related asset optimization

### Performance Optimizations

- **Code Splitting**: Form components loaded on demand
- **Bundle Analysis**: Form-specific bundle optimization
- **Caching Strategy**: Form configuration and validation caching
- **CDN Integration**: Form asset delivery optimization

## GitHub Repository Structure

### Branch Strategy

- **main**: Production-ready form code
- **develop**: Integration branch for form features
- **feature/forms-\***: Form-specific feature branches
- **hotfix/forms-\***: Critical form bug fixes

### Key Files and Directories

```
├── .github/workflows/          # CI/CD pipelines
├── src/systems/AltusForms/     # Form system components
├── src/lib/componentsRouter/   # Form routing logic
├── src/redux/slices/          # Form state management
├── docs/forms-*               # Form system documentation
├── tests/forms/               # Form-specific tests
└── package.json               # Form dependencies
```

### Development Workflow

1. **Feature Branch**: Create from develop
2. **Form Development**: Local development with hot reload
3. **Form Testing**: Unit and integration tests
4. **Code Review**: Pull request review process
5. **Integration**: Merge to develop branch
6. **Staging Deployment**: Automatic deployment to staging
7. **Production**: Manual promotion to production

## Contentful Configuration

### Content Models

#### 1. ComponentForm Content Type

```graphql
{
  template: String              # Form template type
  endpointUrl: String          # Form submission endpoint
  sfmcUrl: String              # SFMC integration URL
  formCategory: String         # Form categorization
  header: RichText             # Form header content
  footer: RichText             # Form footer content
  thankYouMessage: RichText    # Success message
  loadingMessage: RichText     # Loading state message
  successMessage: RichText     # Success state message
  errorMessage: RichText       # Error state message
  backgroundImage: Asset       # Form background image
  displayAt: String            # Display timing configuration
  isStatic: Boolean           # Static form flag
  isLightMode: Boolean        # Light theme flag
  displayPostAction: Boolean   # Post-submission action flag
  isDynamicAgreement: Boolean  # Dynamic agreement flag
  formFieldsCollection: [FormField]  # Form field configuration
  hiddenFieldsCollection: [HiddenField]  # Hidden field configuration
}
```

#### 2. FormField Content Type

```graphql
{
  altusFieldType: String       # Field type (Text, Email, Dropdown, etc.)
  altusFieldName: String       # Field name for form submission
  altusLabel: String           # Field label text
  altusIsRequired: Boolean     # Required field flag
  placeholderText: String      # Placeholder text
  validationErrorMessage: String  # Custom error message
  helperText: String           # Helper text
  isAltusEmailAllowed: Boolean # Allow Altus email domains
  isGenericEmailAllowed: Boolean # Allow generic email domains
  altusDataFormat: String      # Data format (Phone, etc.)
  sectionItemsCollection: [FormSection]  # Multi-step sections
}
```

### Form Configuration Strategy

- **Template Selection**: Choose appropriate form template for use case
- **Field Configuration**: Configure field types, validation, and styling
- **Integration Setup**: Configure submission endpoints and data mapping
- **Analytics Setup**: Configure tracking events and conversion goals

### Content Publishing Workflow

1. **Form Creation**: Create ComponentForm entry in Contentful
2. **Field Configuration**: Add and configure form fields
3. **Template Selection**: Choose appropriate form template
4. **Integration Setup**: Configure submission endpoints
5. **Preview Testing**: Test form in staging environment
6. **Publication**: Publish form to production
7. **Analytics Validation**: Verify tracking implementation
