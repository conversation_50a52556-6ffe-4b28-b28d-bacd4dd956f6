const fs = require('fs');
const path = require('path');

// Path to the page.tsx file
const filePath = path.join(__dirname, '../src/app/[[...slug]]/page.tsx');

// New revalidate time
const newRevalidateTime = 3600; // Set your desired revalidation time in seconds

// Read the file
fs.readFile(filePath, 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading file:', err);
    return;
  }

  // Regex to match 'export const revalidate = <number>'
  const regex = /(export const revalidate = )(\d+)/;

  // Replace the existing revalidation time
  const updatedData = data.replace(regex, `$1${newRevalidateTime}`);

  // Write the updated data back to the file
  fs.writeFile(filePath, updatedData, 'utf8', (err) => {
    if (err) {
      console.error('Error writing file:', err);
      return;
    }

    console.log(`Revalidation time updated to ${newRevalidateTime} seconds.`);
  });
});
