importScripts('https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js')
importScripts(
  'https://www.gstatic.com/firebasejs/9.6.1/firebase-messaging-compat.js'
)

const firebaseConfig = {
  apiKey: 'AIzaSyAgbBGhq56eQXGJeumZFLmyGXoANqVeRME',
  authDomain: 'agl-pocs.firebaseapp.com',
  databaseURL: 'https://agl-pocs-default-rtdb.firebaseio.com',
  projectId: 'agl-pocs',
  storageBucket: 'agl-pocs.firebasestorage.app',
  messagingSenderId: '164346411824',
  appId: '1:164346411824:web:cbd2351b3cdb5c1bdae6f5',
  measurementId: 'G-GVPQRF3CYC',
}

firebase.initializeApp(firebaseConfig)
const messaging = firebase.messaging()

messaging.onBackgroundMessage(async (payload) => {
  // console.log(
  //   '[firebase-messaging-sw.js] Received background message ',
  //   payload.data
  // )
  // try {
  //   if (payload?.data?.isSystemNotification === 'true') return
  //   // Fetch data from IndexedDB
  //   const preferences = await readFromIndexedDB('preferences')
  //   const allowedPreferences = preferences ? JSON.parse(preferences) : {}
  //   if (allowedPreferences?.noBrowserNoti === 'true') return
  //   // Parse the payload data
  //   const payloadCategory = JSON.parse(payload?.data?.category || '[]')
  //   const isCategoryAllowed =
  //     Array.isArray(payloadCategory) &&
  //     payloadCategory?.some((category) =>
  //       allowedPreferences?.allowedNotificationTypes?.includes(category)
  //     )
  //   if (
  //     isCategoryAllowed &&
  //     payload?.data?.type &&
  //     payload?.data?.type !== 'page'
  //   ) {
  //     const notificationTitle = payload?.data?.title
  //     let notificationOptions = {
  //       body: payload?.data?.body,
  //       icon: payload?.data?.icon,
  //       data: {
  //         url: payload?.data?.url,
  //       },
  //       vibrate: [
  //         500, 110, 500, 110, 450, 110, 200, 110, 170, 40, 450, 110, 200, 110,
  //         170, 40, 500,
  //       ],
  //       silent: false,
  //       // actions: [
  //       //   { action: 'action1', title: 'Action 1' },
  //       //   { action: 'action2', title: 'Action 2' },
  //       // ],
  //     }
  //     if (payload?.data?.tag !== '') {
  //       notificationOptions.tag = payload?.data?.tag
  //       notificationOptions.renotify = true
  //     }
  //     if (payload?.data?.badge) {
  //       notificationOptions.badge = payload?.data?.badge
  //     }
  //     if (payload?.data?.attentionRequired) {
  //       notificationOptions.requireInteraction =
  //         payload?.data?.attentionRequired === 'true'
  //     }
  //     self.registration.showNotification(notificationTitle, notificationOptions)
  //   } else {
  //     console.log(
  //       '[firebase-messaging-sw.js] Notification category not allowed or invalid type'
  //     )
  //   }
  // } catch (error) {
  //   console.error(
  //     '[firebase-messaging-sw.js] Error processing background message: ',
  //     error
  //   )
  // }
})

/**
 * Handle notification click event.
 * @param {Object} event - The notification click event.
 * @returns {Promise<void>} - Resolves when the notification is closed and the window is opened.
 * @description
 * 1. Close the notification.
 * 2. Send the notification click analytics.
 * 3. Check if the window is already open and focus it.
 * 4. If the window is not open, open it and focus it.
 */
// function handleClick(event) {
//   event.notification.close()
//   const url = event?.notification?.data?.url

//   sendNotificationAnalytics('click', event?.notification)

//   event.waitUntil(
//     self?.clients
//       .matchAll({ type: 'window', includeUncontrolled: true })
//       .then((clientsArr) => {
//         const hadWindowToFocus = clientsArr?.some((windowClient) => {
//           return windowClient?.url === url
//             ? (windowClient?.focus(), true)
//             : false
//         })

//         if (!hadWindowToFocus)
//           self?.clients
//             ?.openWindow(url)
//             ?.then((windowClient) =>
//               windowClient ? windowClient?.focus() : null
//             )
//       })
//   )
// }

// self.addEventListener('notificationclick', handleClick)

// self.addEventListener('notificationclose', (event) => {
//   sendNotificationAnalytics('close', event?.notification)
// })

// function generateUniqueId(length = 16) {
//   const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
//   let result = ''
//   for (let i = 0; i < length; i++) {
//     result += chars.charAt(Math.floor(Math.random() * chars.length))
//   }
//   return result
// }

/**
 * Sends notification analytics to the server.
 * @param {string} eventType - The type of event to log, either 'click' or 'close'.
 * @param {any} notification - The notification object containing the data to log.
 *
 * The function sends a POST request to the '/api/send-notification-analytics' endpoint
 * with a JSON payload containing the client_id, event name, and event params.
 * The event name is in the format 'notification_<eventType>', and the params object
 * contains the title, description, type, and url of the notification.
 *
 * If the request fails, an error is logged to the console.
 */
// async function sendNotificationAnalytics(eventType, notification) {
//   let eventString = {
//     click: 'click',
//     close: 'close',
//   }

//   let payload = {
//     client_id: generateUniqueId(),
//     events: [
//       {
//         name: `notification_${eventString[eventType]}`,
//         params: {
//           title: notification?.title,
//           description: notification?.body,
//           type:
//             notification?.type === 'both' ? 'Page & Browser' : 'Only Browser',
//           url: notification?.data?.url || '',
//         },
//       },
//     ],
//   }

//   try {
//     await fetch('/api/send-notification-analytics', {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify(payload),
//     })
//   } catch (error) {
//     console.error('Error sending analytics:', error)
//   }
// }

/**
 * Reads data from IndexedDB for a given key.
 * Opens the 'notification-preferences' database and retrieves the value
 * associated with the specified key from the 'syncStore' object store.
 *
 * @param {string} key - The key of the data to be read from IndexedDB. Defaults to 'preferences'.
 * @returns {Promise<any>} A promise that resolves with the value associated with the key, or null if not found.
 *                         If an error occurs during the operation, the promise is rejected with an error message.
 */

// async function readFromIndexedDB(key = 'preferences') {
//   return new Promise((resolve, reject) => {
//     const request = indexedDB?.open('notification-preferences', 1)

//     request.onsuccess = function () {
//       const db = request?.result
//       const tx = db?.transaction('syncStore', 'readonly')
//       const store = tx?.objectStore('syncStore')
//       const getRequest = store?.get(key)

//       getRequest.onsuccess = () => {
//         resolve(getRequest?.result?.value || null)
//       }

//       getRequest.onerror = () => {
//         reject('IndexedDB get error')
//       }
//     }

//     request.onerror = () => {
//       reject('IndexedDB open error')
//     }
//   })
// }
