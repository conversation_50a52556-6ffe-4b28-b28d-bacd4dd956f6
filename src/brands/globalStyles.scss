@charset "UTF-8";

/** globalStylesV4 is made for v4, it uses v4 naming convention for future.
There are instances where variable and classNames are used from "_vars.scss" but in future when we revisit,
we will consolidate both vars and vars2 into one and "_vars2.scss" will replace old "_vars.scss" */

@import "globalStylesV4";

//Define global styles that are common to all the Brand components here and then import it into every component.

/**
Default variables are prepended in next.config.js
 */

/**
Background styles
 */

.bnone {
  background: none;
  }

.bgTranslucent {
  backdrop-filter: blur(1rem);
  }

.bgTransparent {
  background-color: transparent !important;
  }

.bp1 {
  background-color: $cp1;
  }

.bp2 {
  background-color: $cp2;
  }

.bp3 {
  background-color: $cp3;
  }

.bp6 {
  background-color: $cp6;
  }

.bp6 {
  background-color: $cp6;
  }

.bs1 {
  background-color: $cs1;
  }

.bs2 {
  background-color: $cs2;
  }

.bs3 {
  background-color: $cs3;
  }

.bs4 {
  background-color: $cs7;
  }

.bs5 {
  background-color: $cs5;
  }

.bn1 {
  background-color: $cn1;
  }

.bn2 {
  background-color: $cn2;
  }

.bn3 {
  background-color: $cn3;
  }

.bn4 {
  background-color: $cn4;
  }

.bn5 {
  background-color: $cn5;
  }

.bn6 {
  background-color: $cn6;
  }

.bn7 {
  background-color: $cn7;
  }

.bn8 {
  background-color: $cn8;
  }

/**
Colours
 */

.cp1 {
  color: $cp1;
  }

.cp2 {
  color: $cp2;
  }

.cp3 {
  color: $cp3;
  }

.cp4 {
  color: $cp4;
  }

.cp5 {
  color: $cp5;
  }

.cp6 {
  color: $cp6;
  }

.cp6 {
  color: $cp6;
  }

.cs1 {
  color: $cs1;
  }

.cs2 {
  color: $cs2;
  }

.cs3 {
  color: $cs3;
  }

.cs4 {
  color: $cs4;
  }

.cs5 {
  color: $cs5;
  }

.cs6 {
  color: $cs6;
  }

.cs7 {
  color: $cs7;
  }


.cn1 {
  color: $cn1;
  }

.cn2 {
  color: $cn2;
  }

.cn3 {
  color: $cn3;
  }

.cn4 {
  color: $cn4;
  }

.cn5 {
  color: $cn5;
  }

.cn6 {
  color: $cn6;
  }

.cn7 {
  color: $cn7;
  }

.cn8 {
  color: $cn8;
  }

/**
Gradients
 */
.bs1s3d {
  background: linear-gradient(45deg, $cs1, $cs3);
  }

.bs1s3h {
  background: linear-gradient(90deg, $cs1, $cs3);
  }

.bp2s3d {
  background: linear-gradient(45deg, $cp2, $cs3);
  }

.bp1p2d {
  background: linear-gradient(45deg, $cp1, $cp2);
  }

.bp2p1r {
  background: linear-gradient(263deg, #0028d7 -30.62%, #000b3d 72.65%);
  }

.bo1n1d {
  background: linear-gradient(45deg, $co1, $cn1);
  opacity: 0.6;
  }

.bo1s2d {
  background: linear-gradient(45deg, $co1, $cs2);
  opacity: 0.6;
  }

.cs1s3d {
  background: linear-gradient(45deg, $cs1, $cs3);
  background-clip: text;
  color: transparent;
  }

.cs1s3h {
  background: linear-gradient(90deg, $cs1, $cs3);
  background-clip: text;
  color: transparent;
  }


/**
FontFamilies
 */

// cambon fonts
.fSerif {
  font-family: $fSerif;
  }

// sans-serif fonts
.fSans {
  font-family: $fSans;
  }

// aeonik regular fonts
.fSansReg {
  font-family: $fSansReg;
  }

// aeonik regular italic fonts
.fSansRegItalic {
  font-family: $fSansRegItalic;
  }

// aeonik regular light fonts
.fSansLight {
  font-family: $fSansLight;
  }

// aeonik regular light italic fonts
.fSansLightItalic {
  font-family: $fSansLightItalic;
  }

// aeonik medium fonts
.fSansMed {
  font-family: $fSansMed;
  }

// aeonik medium italic fonts
.fSansMedItalic {
  font-family: $fSansMedItalic;
  }

// aeonik bold fonts
.fSansBld {
  font-family: $fSansBld;
  }

// aeonik bold italic fonts
.fSansBldItalic {
  font-family: $fSansBldItalic;
  }

/**
FontSized
 */

.fs1 {
  font-size: $fs1;
  line-height: 20px;
  }

// body extra small
.fs2 {
  font-size: $fs2;
  // line-height: 20px;
  line-height: 22px;
  }

// body small
.fs3 {
  font-size: $fs3;
  // line-height: 22px;
  line-height: 24px;
  }

// body medium
.fs4 {
  font-size: $fs4;
  // line-height: 24px;
  line-height: 26px;
  }

// body large
.fs5 {
  font-size: $fs5;
  line-height: 28px;
  }

// body extra large
.fs6 {
  font-size: $fs6;
  line-height: 32px;
  }

/**
Borders
 */
.bdr {
  border-width: 1px;

  &.solid {
    border-style: solid;
    }

  &.dashed {
    border-style: dashed;
    }

  &.transparent {
    border: transparent;
    }

  &.translucent {
    backdrop-filter: blur(1rem);
    -webkit-backdrop-filter: blur(1rem);
    background: linear-gradient(45deg,
            rgba(0, 150, 255, 0.15),
            rgba(250, 255, 155, 0.15));
    }

  &.thick {
    border-width: 16px; //implement according to breakpoints
    }
  }

/**
Edges
 */
.rounded {
  &.min {
    border-radius: 5px;
    }

  &.s {
    border-radius: 10px;
    }

  &.m {
    border-radius: 15px;
    }

  &.l {
    border-radius: 20px;
    }

  &.xl {
    border-radius: 30px;
    }

  &.max {
    border-radius: 500px;
    }

  @media screen and (max-width: $mScreenSize) {
    &.xl {
      border-radius: 15px;
      }
    }
  }

*,
*::before,
*::after {
  box-sizing: border-box;
  font-weight: inherit;
  }

html,
body {
  margin: 0;
  padding: 0;
  scroll-behavior: smooth;
  // display: flex;
  // justify-content: center;
  // align-items: center;
  // height: 100vh;
  // width: 100%;
  font-family: $fSansReg;
  // gap: 3rem;
  }

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  }

//
// h1,
// h2 {
//
//   }
//
// h3,
// h4,
// h5,
// h6 {
//
//   }

h1 {
  font-size: $h1FontSize;
  line-height: 74px;
  @extend .fSerif;
  }

h2,
.h2 {
  font-size: $h2FontSize;
  line-height: 52px;
  @extend .fSansBld;
  }

h3,
.h3 {
  font-size: $h3FontSize;
  line-height: 40px;
  @extend .fSansBld;
  }

h4,
.h4 {
  font-size: $h4FontSize;
  line-height: 30px;
  @extend .fSansBld;
  }

h5,
.h5 {
  font-size: $h5FontSize;
  line-height: 26px;
  @extend .fSansBld;
  }

h6,
.h6 {
  font-size: $h6FontSize;
  line-height: 22px;
  @extend .fSansBld;
  }

.h1 {
  @extend .fSerif;
  @extend h1;
  }

// .h2 {
//   // @extend h2;
//   font-size: $h2FontSize;
//   line-height: 52px;
//   }

// .h3 {
//   @extend h3;
//   }
//
// .h4 {
//   @extend h4;
//   }
//
// .h5 {
//   @extend h5;
//   }
//
// .h6 {
//   @extend h6;
//   }

.fs7 {
  font-size: $fs7;
  }

.subheading {
  font-size: $fs4;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  font-family: $fSansReg;
  margin: 0 0 32px 0;
  }

.quote {
  font-size: 22px;
  line-height: 30px;
  font-family: $fSansMedItalic;
  }

/**
Padding
 */

.p0 {
  padding: 0;
  }

/**
margin
 */

.m0 {
  margin: 0;
  }

/**
p tag margin 0
 */

p {
  margin: 0;
  }

/**
Shadows
 */
.shadowNone {
  box-shadow: none !important;
  }

.shadow1 {
  box-shadow: rgba(0, 0, 0, 0.16) 0 10px 36px 0, rgba(0, 0, 0, 0.06) 0 0 0 1px;
  }

.shadow2 {
  box-shadow: rgba(100, 100, 111, 0.2) 0 7px 7px 0;
  }

.shadow3 {
  box-shadow: rgba(10, 44, 55, 0.1) 0 5px 10px 1px;
  }


//@todo change color name from defaults
.interactive {
  &:hover {
    //@extend .bn5
    cursor: pointer;
    }

  &:focus {
    outline: dashed 2px $cp2;
    }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    }
  }

.bleedf {
  grid-column: 1 / 4 !important;
  }

.bleedr {
  grid-column: 2 / 4 !important;
  }

.bleedl {
  grid-column: 1 / 3 !important;
  }

.hidden {
  visibility: hidden !important;
  }

.dNone {
  display: none !important;
  }

.highlight {}

.hotspot {}

.draggable {}

.pointer {
  cursor: pointer;
  }

.simpleCta:hover .arrowHead,
.link:hover .arrowHead {
  transform: translateX(3px);
  }

.simpleCta:hover .arrowBody,
.link:hover .arrowBody {
  opacity: 1;
  transform: scaleX(2);
  }

.card .link p {
  font-family: $fSansBld;
  letter-spacing: 1px;
  }

.arrowShow:hover .arrowHead,
.arrowShow:hover .arrowHead {
  transform: translateX(3px);
  }

.arrowShow:hover .arrowBody,
.arrowShow:hover .arrowBody {
  opacity: 1;
  transform: scaleX(2);
  }

.preview-on-hover .arrowHead{
  transform: translateX(3px);
  }

.preview-on-hover .arrowBody {
  opacity: 1;
  transform: scaleX(2);
  }
.preview-on-hover .cta-z12{
  position: relative;
  z-index: 12;
}
.preview-on-hover .generic-link{
    color: $cs2;
    background-color: $cp2;
  }

.preview-on-hover .generic-link-dark{
    color: $cn2;
    background-color: $cs6;
  }

.preview-on-hover .next-link{
   color: $cp2;
   text-decoration: underline;
  }
.preview-on-hover .next-link-dark{
   color: $cs1;
   text-decoration: underline;
  }

.preview-on-hover .primary{
  color: $cn8;
  background-color: $cp3;
  }

.preview-on-hover .primary-dark{
  color: $cn2;
  background-color: $cs4;
  }
.preview-on-hover .secondary{
   background-color: $cp3;
    color: $cn8;
  }
.preview-on-hover .secondary-dark{
   background-color: $cs4;
    color: $cn2;
  }

.preview-on-hover .tertiary{
   color: $cp2;
  }

.preview-on-hover .tertiary-dark{
   color: $cs1;
  }

.preview-on-hover .tertiary2{
  color: $cp2;
  }

.preview-on-hover .tertiary2-dark{
   color: $cs1;
  }

.preview-on-hover .tertiary3{
   color: $cs2;
  }

.preview-on-hover .tertiary3-dark{
   color: $cs2;
  }

.preview-icon-root .preview-icon{
    height: 24px !important;
    width: 24px !important;
  }

.bold .link p {
  font-family: $fSansBld;
  letter-spacing: 1px;
  }

.textLeft {
  text-align: left;
  }

.textRight {
  text-align: right;
  }

.textCenter {
  text-align: center;
  }

.trendingArticles:hover {
  & .trendingCard:not(:hover) {

    h1,
    p,
    h6 {
      color: $cn2;
      }
    }

  & .DarkTrendingCard:not(:hover) {

    h1,
    p,
    h6 {
      color: $cs2;
      }
    }
  }

.nav {
  color: $cp2;
  }

.darkNav {
  color: $cs1;
  }

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  }

.block {
  display: block;
  }

.inline {
  display: inline;
  }

.inlineBlock {
  display: inline-block;
  }

.g4 {
  gap: $s6;

  @media screen and (max-width: $mScreenSize) {
    gap: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   gap: $s3;
  //   }
  }

.g3 {
  gap: $s5;

  @media screen and (max-width: $mScreenSize) {
    gap: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   gap: $s3;
  //   }
  }

.g2 {
  gap: $s4;

  // @media screen and (max-width: $smScreenSize) {
  //   gap: $s3;
  //   }
  }

.g1 {
  gap: $s3;
  }

.g0 {
  gap: 0 !important;
  }

.gx0 {
  column-gap: 0px !important;
  }

.gy0 {
  row-gap: 0px !important;
  }

.p3 {
  padding: $s5;

  @media screen and (max-width: $mScreenSize) {
    padding: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   padding: $s3;
  //   }
  }

.p2 {
  padding: $s4;

  // @media screen and (max-width: $smScreenSize) {
  //   padding: $s3;
  //   }
  }

.p1 {
  padding: $s3;
  }

.p0 {
  padding: 0 !important;
  }

.px0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
  }

.py0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
  }

.py3 {
  padding-top: $s5;
  padding-bottom: $s5;

  @media screen and (max-width: $mScreenSize) {
    padding-top: $s4;
    padding-bottom: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   padding-top: $s3;
  //   padding-bottom: $s3;
  //   }
  }

.py2 {
  padding-top: $s4;
  padding-bottom: $s4;

  // @media screen and (max-width: $smScreenSize) {
  //   padding-top: $s3;
  //   padding-bottom: $s3;
  //   }
  }

.py4 {
  padding-top: $s6 !important;
  padding-bottom: $s6 !important;

  @media screen and (max-width: $mScreenSize) {
    padding-top: $s4 !important;
    padding-bottom: $s4 !important;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   padding-top: $s3;
  //   padding-bottom: $s3;
  //   }
  }

.py1 {
  padding-top: $s3;
  padding-bottom: $s3;
  }

.py20 {
  padding-top: 20px;
  padding-bottom: 20px;
  }

.px3 {
  padding-left: $s5;
  padding-right: $s5;

  @media screen and (max-width: $mScreenSize) {
    padding-left: $s4;
    padding-right: $s4;
    }

  @media screen and (max-width: $smScreenSize) {
    padding-left: $s3;
    padding-right: $s3;
    }
  }

.px2 {
  padding-left: $s4;
  padding-right: $s4;

  @media screen and (max-width: $smScreenSize) {
    padding-left: $s3;
    padding-right: $s3;
    }
  }

.px1 {
  padding-left: $s3;
  padding-right: $s3;
  }

.pt4 {
  padding-top: $s6 !important;

  @media screen and (max-width: $mScreenSize) {
    padding-top: $s4 !important;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-top: $s3;
  //   }
  }

.pt3 {
  padding-top: $s5;

  @media screen and (max-width: $mScreenSize) {
    padding-top: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   padding-top: $s3;
  //   }
  }

.pt2 {
  padding-top: $s4;

  // @media screen and (max-width: $smScreenSize) {
  //   padding-top: $s3;
  //   }
  }

.pt1 {
  padding-top: $s3;
  }

.pb4 {
  padding-bottom: $s6 !important;

  @media screen and (max-width: $mScreenSize) {
    padding-bottom: $s4 !important;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-top: $s3;
  //   margin-bottom: $s3;
  //   }
  }

.pb3 {
  padding-bottom: $s5;

  @media screen and (max-width: $mScreenSize) {
    padding-bottom: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   padding-bottom: $s3;
  //   }
  }

.pb2 {
  padding-bottom: $s4;

  // @media screen and (max-width: $smScreenSize) {
  //   padding-bottom: $s3;
  //   }
  }

.pb1 {
  padding-bottom: $s3;
  }


.defaultLayout {
  gap: $s4;

  // @media screen and (max-width: $smScreenSize) {
  //   gap: $s3;
  //   }

  // @media screen and (min-width: 1933.33px) {
  //   justify-content: space-between;
  // }
  }

// .hero:first-of-type {
//   margin-top: -80px;
// }

.mt4 {
  margin-top: $s6;

  @media screen and (max-width: $mScreenSize) {
    margin-top: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-top: $s3;
  //   }
  }

.mt3 {
  margin-top: $s5;

  @media screen and (max-width: $mScreenSize) {
    margin-top: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-top: $s3;
  //   }
  }

.mt2 {
  margin-top: $s4;

  // @media screen and (max-width: $smScreenSize) {
  //   margin-top: $s3;
  //   }
  }

.mt1 {
  margin-top: $s3;
  }

.mb4 {
  margin-bottom: $s6;

  @media screen and (max-width: $mScreenSize) {
    margin-bottom: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-bottom: $s3;
  //   }
  }

.mb3 {
  margin-bottom: $s5;

  @media screen and (max-width: $mScreenSize) {
    margin-bottom: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-bottom: $s3;
  //   }
  }

.mb2 {
  margin-bottom: $s4;

  // @media screen and (max-width: $smScreenSize) {
  //   margin-bottom: $s3;
  //   }
  }

.mb1 {
  margin-bottom: $s3;
  }


.my3 {
  margin-top: $s5;
  margin-bottom: $s5;

  @media screen and (max-width: $mScreenSize) {
    margin-top: $s4;
    margin-bottom: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-top: $s3;
  //   margin-bottom: $s3;
  //   }
  }

.my4 {
  margin-top: $s6;
  margin-bottom: $s6;

  @media screen and (max-width: $mScreenSize) {
    margin-top: $s4;
    margin-bottom: $s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-top: $s3;
  //   margin-bottom: $s3;
  //   }
  }

.mt-1 {
  margin-top: -$s3;
  }

.mt-2 {
  margin-top: -$s4;

  // @media screen and (max-width: $smScreenSize) {
  //   margin-top: $s3;
  //   }
  }


.mt-3 {
  margin-top: -$s5;

  @media screen and (max-width: $mScreenSize) {
    margin-top: -$s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-top: -$s3;
  //   }
  }

.mt-4 {
  margin-top: -$s6 !important;

  @media screen and (max-width: $mScreenSize) {
    margin-top: -$s4 !important;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-top: -$s3;
  //   }
  }

.mb-1 {
  margin-bottom: -$s3;
  }

.mb-2 {
  margin-bottom: -$s4;

  // @media screen and (max-width: $smScreenSize) {
  //   margin-bottom: $s3;
  //   }
  }

.mb-3 {
  margin-bottom: -$s5;

  @media screen and (max-width: $mScreenSize) {
    margin-bottom: -$s4;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-bottom: -$s3;
  //   }
  }

.mb-4 {
  margin-bottom: -$s6 !important;

  @media screen and (max-width: $mScreenSize) {
    margin-bottom: -$s4 !important;
    }

  // @media screen and (max-width: $smScreenSize) {
  //   margin-bottom: -$s3;
  //   }
  }


.secNav:hover {
  .secNavChild:not(:hover) {
    p {
      color: $cn4;
      }
    }
  }

/**
SMALL SCREEN STYLING BEGINS
 */

@media (max-width: $smScreenSize), (max-height: $smScreenHeight) {

  h1,
  .h1 {
    font-size: $sH1FontSize;
    line-height: 50px;
    }

  h2 {
    font-size: $sH2FontSize;
    line-height: 40px;
    }

  h3,
  .h3 {
    font-size: $sH3FontSize;
    line-height: 32px;
    }

  h4,
  .h4 {
    font-size: $sH4FontSize;
    line-height: 26px;
    }

  h5,
  .h5 {
    font-size: $sH5FontSize;
    line-height: 24px;
    }

  h6,
  .h6 {
    font-size: $sH6FontSize;
    line-height: 22px;
    }

  .fs7 {
    font-size: $sfs7;
    line-height: 60px;
    }

  // .h1 {
  //   @extend h1;
  //   }

  .h2 {
    // @extend h2;
    font-size: $h2FontSize;
    line-height: 52px;
    }

  // .h3 {
  //   @extend h3;
  //   }
  //
  // .h4 {
  //   @extend h4;
  //   }
  //
  // .h5 {
  //   @extend h5;
  //   }
  //
  // .h6 {
  //   @extend h6;
  //   }

  .fs5 {
    font-size: $fs4;
    line-height: 24px;
    }

  .fs6 {
    font-size: $fs5;
    line-height: 28px;
    }

  .fs4,
  .contenter p {
    line-height: 24px !important;
    }


  // .hero:first-of-type {
  //   margin-top: 0;
  // }
  }


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
  }

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
  }

.mb-80 {
  margin-bottom: -80px;

  @media (max-width: $smScreenSize) {
    margin-bottom: 0;
    }
  }

.mb-100 {
  margin-bottom: -100px;

  @media (max-width: $smScreenSize) {
    margin-bottom: 0;
    }
  }

.contenter p {
  font-size: $fs4;
  line-height: 26px;
  // font-family: $fSansLight;;
  // letter-spacing: 24px;
  }

.content .contenter p:not(:first-of-type) {
  margin-top: 18px;
  }

.ITemplate {

  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: normal !important;
    }
  }

.PRITemplate {
  .contenter {
    h2 {
      font-family: $fSansBld;
      }

    p {
      line-height: 30px !important;
      }

    .templateLink {
      &:hover {
        color: $cs2;
        background-color: $cp2;
        text-decoration: none;
        transition: color 0.25s, background-color 0.25s;
        }
      }
    }

  .darkTheme {
    .templateLink {

      &:hover {
        color: $cp1;
        background-color: $cs1;
        text-decoration: underline;
        text-decoration: none;
        transition: color 0.25s,
        background-color 0.25s;
        }
      }
    }

  }

.placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #eee;
  border-radius: 4px;
  }

@keyframes placeHolderShimmer {
  0% {
    background-position: -468px 0
    }

  100% {
    background-position: 468px 0
    }
  }

.placeholderanim {
  animation-duration: 1.25s;
  animation-fill-mode: forwards;
  animation-iteration-count: 4;
  animation-name: placeHolderShimmer;
  animation-timing-function: linear;
  background: darkgray;
  background: linear-gradient(to right, #eee 10%, #ddd 18%, #eee 33%);
  background-size: 800px 104px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  }

.cardLayout {
  @media screen and (min-width: 1600px) {
    display: none !important;
    }
  }

.banim1 {
  @include banim1;
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  }

.posRel {
  position: relative;
  }

.z6 {
  z-index: 6;
  }

.z7 {
  z-index: 7;
  }

.z-1 {
  z-index: -1;
  }


.backToTop {
  position: fixed;
  bottom: 20px;
  z-index: 9;
  transition: .5s ease;
  // border: 1px solid $cs2;

  &:hover {
    transition: transform 250ms cubic-bezier(0.33, 0.00, 0.00, 1.00);
    transform: scale(1.2);
    }

  @media (max-width: $smScreenSize) {
    right: 10px;
    bottom: 10px;
    }
  }

.backToTopLeft {
  left: 20px;
  }

.backToTopRight {
  right: 20px;
  }

.backDropBlur {
  backdrop-filter: blur(1rem);
  }

.portraitImgInColumn {
  height: auto;

  .portraitImgInColumn {
    height: 100%;

    @media (max-width: $smScreenSize) {
      height: 350px;
      }

    @media (max-width: 550px) {
      height: 200px;
      }

    }
  }

.formLandingPage {
  padding: 0 !important;
  max-width: 100% !important;
  box-shadow: none !important;
  }

.w-fit {
  width: fit-content;
  }

.w-max {
  width: max-content;
  }

// input:-webkit-autofill,
// input:-webkit-autofill:hover,
// input:-webkit-autofill:focus,
// input:-webkit-autofill:active {
//   -webkit-background-clip: text;
//   -webkit-text-fill-color: #fff;
//   transition: background-color 5000s ease-in-out 0s;
//   box-shadow: inset 0 0 20px 20px #23232329;
//   }

// input:-webkit-autofill,
// input:-webkit-autofill:hover,
// input:-webkit-autofill:focus,
// input:-webkit-autofill:active {
//   transition: background-color 9999s ease-in-out 0s;
//   }
#domainReonomyCom {
  .header {
    .chevron2ArrowRoot {
      margin-top: 0 !important;
      }
    }
  }

.simpleCta[type="submit"] span {
  text-wrap: nowrap;
  }


@keyframes pulsate {
  0% {
    transform: scale(1, 1);
    opacity: 1;
    }

  50% {
    opacity: 0.7;
    transform: scale(1.03, 1.2);
    }


  100% {
    opacity: 0.2;
    transform: scale(1.04, 1.3);
    }
  }

.pulsate {
  animation: pulsate 1.75s ease-in-out infinite;
  border-radius: 3px;
  position: absolute;
  top: 0;
  // border: 1px solid $cs1;
  width: 100%;
  z-index: -1;
  background: linear-gradient(45deg, $cs1, $cs3);
  height: 50px;

  }

.argusFormPadding {
  padding: 60px 60px 30px !important;

  @media (max-width: $smScreenSize) {
    padding: 30px 30px 0px !important;
    }
  }


.videoColWidthControls {
  height: 50px !important;
  bottom: 10px !important;

  @media (max-width: $mScreenSize) {
    bottom: 15px !important;
    }

  .videoColWidthProgress {
    padding: 5px 0 !important;
    }

  }

.videoColWidthControlInner {
  width: 80% !important;
  }

strong {
  // font-weight: bold !important;
  font-family: $fSansBld;
  }

.awardLayout {
  margin-top: -60px;

  @media (max-width: $mScreenSize) {
    margin-top: -30px;
    }
  }

.gradientBorder {
  position: relative;
  border-radius: 30px;
  padding: 5px;
  background: linear-gradient(to right, $cp2, $cs3);
  overflow: hidden;
  }

.gradientBorder > * {
  position: relative;
  border-radius: 26px;
  background: white;
  width: 100%;
  height: 100%;
  }