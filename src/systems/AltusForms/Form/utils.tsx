
import { getAgreementDetail } from '../../../../actions/getAgreementDetail'
import {
  getLocalStorageItem,
  getQueryParamJson,
  getSessionStorageItem,
  isBrowser,
  isProduction,
  replace<PERSON>ey,
  setLocalStorageItem,
} from '../../../globals/utils'
import { MCA_LINKS } from '../../../lib/constant'
import {
  deleteFormActivation,
  setFormData,
  setProgress,
} from '../../../redux/slices/formSlice'
import { setImmersiveVideoData } from '../../../redux/slices/popupSlice'
import store from '../../../redux/store'
import {
  formErrorEvent,
  formSuccessEvent,
  getClientIdFromCookie,
  getMeasurementId,
} from '../../../utils/analyticsEvents'
import { AltusEmails } from './notIncludeEmails'

export function postToPardot({
  values,
  setFormSubmitionState,
  sfmcUrl,
  endpointUrl,
  contentfulId,
  isDisplayPostAction,
  formCategory,
  formData,
}: any) {
  const urlencoded = new URLSearchParams()

  //Reonomy visage to SFMC field mapping
  let updatedValues = replaceKey(values, 'company_name', 'company')
  updatedValues = replaceKey(values, 'job_title', 'jobtitle')
  updatedValues = replaceKey(values, 'email', 'Email')
  updatedValues = replaceKey(values, 'first_name', 'firstname')
  updatedValues = replaceKey(values, 'last_name', 'lastname')
  updatedValues = { ...updatedValues, sfmcUrl }

  if (updatedValues) {
    if (updatedValues.hasOwnProperty('password')) {
      delete updatedValues['password']
    }

    if (updatedValues.hasOwnProperty('sfid') && updatedValues['sfid']) {
      updatedValues['campaignid'] = updatedValues['sfid']
    }

    Object.keys(updatedValues).map((key) => {
      urlencoded.append(key, updatedValues[key])
    })
  }

  if (process.env.NEXT_PUBLIC_FORM_SYSTEM === 'PARDOT') {
    const iframe = document.createElement('iframe')
    iframe.src = `${endpointUrl}?${urlencoded}`
    iframe.onload = function () {
      try {
        const iframeDocument =
          iframe.contentDocument || iframe.contentWindow?.document
        const jsonResponse = JSON.parse(iframeDocument?.body.innerText || '{}')

        if (jsonResponse.status === 'success') {
          setFormSubmitionState('SUCCESS')
          store.dispatch(setFormData(formData))
          if (formData?.setToLocalStorage) {
            setLocalStorageItem(contentfulId, updatedValues)
            window.dispatchEvent(new Event('localStorageChange'))
          }
          if (formData?.postSuccessMessage?.__typename === 'Player') {
            store.dispatch(
              setImmersiveVideoData({
                isImmersiveVideoShow: true,
                ImmersiveVideoProps: { ...formData?.postSuccessMessage },
              })
            )
            const accessedVideo = getLocalStorageItem('__accessedVideo') || {}
            const videoId = formData?.postSuccessMessage?.sys.id
            if (videoId) {
              accessedVideo[videoId] = true
              setLocalStorageItem('__accessedVideo', accessedVideo)
            }
          }

          store.dispatch(
            deleteFormActivation({ contentfulFormId: contentfulId })
          )
          store.dispatch(setProgress({ status: 'SUCCESS' }))
          setTimeout(() => {
            if (!isDisplayPostAction && formData?.template !== 'ExitIntent')
              setFormSubmitionState('THANK YOU')
          }, 5000)

          formSuccessEvent({
            contentfulId,
            formCategory,
            contactInquiryType: values['contactinquirytype'],
            productInterest: values['productinterest'],
          })
        } else {
          setFormSubmitionState('FAILED')
          formErrorEvent({ contentfulId, formCategory })
        }
      } catch (e) {
        setFormSubmitionState('FAILED')
        formErrorEvent({ contentfulId, formCategory })
      }
    }
    iframe.width = '1px'
    iframe.height = '1px'
    document.body.appendChild(iframe)
  } else {
    fetch('/api/form/mkt-cloud/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updatedValues),
    })
      .then((response) => {
        if (response.status === 200) {
          setFormSubmitionState('SUCCESS')
          store.dispatch(setFormData(formData))
          if (formData?.setToLocalStorage) {
            setLocalStorageItem(contentfulId, updatedValues)
            window.dispatchEvent(new Event('localStorageChange'))
          }
          if (formData?.postSuccessMessage?.__typename === 'Player') {
            store.dispatch(
              setImmersiveVideoData({
                isImmersiveVideoShow: true,
                ImmersiveVideoProps: { ...formData?.postSuccessMessage },
              })
            )
            const accessedVideo = getLocalStorageItem('__accessedVideo') || {}
            const videoId = formData?.postSuccessMessage?.sys.id
            if (videoId) {
              accessedVideo[videoId] = true
              setLocalStorageItem('__accessedVideo', accessedVideo)
            }
          }
          store.dispatch(
            deleteFormActivation({ contentfulFormId: contentfulId })
          )
          store.dispatch(setProgress({ status: 'SUCCESS' }))

          setTimeout(() => {
            if (!isDisplayPostAction && formData?.template !== 'ExitIntent')
              setFormSubmitionState('THANK YOU')
          }, 5000)
          formSuccessEvent({
            contentfulId,
            formCategory,
            contactInquiryType: values['contactinquirytype'],
            productInterest: values['productinterest'],
          })
          emailTracking(values['Email'])
        } else {
          setFormSubmitionState('FAILED')
          formErrorEvent({ contentfulId, formCategory })
        }
      })
      .catch((error) => {
        // console.error('error: could not get response of form submission: ', error)
        setFormSubmitionState('FAILED')
        formErrorEvent({ contentfulId, formCategory })
      })
  }

  // to simulate uncomment below code.
  /* setTimeout(() => {
    setFormSubmitionState('SUCCESS')
    store.dispatch(setFormData(formData))
    if (formData?.setToLocalStorage) {
      setLocalStorageItem(contentfulId, updatedValues)
      window.dispatchEvent(new Event('localStorageChange'))
    }

    if (formData?.postSuccessMessage?.__typename === 'Player') {
      store.dispatch(
        setImmersiveVideoData({
          isImmersiveVideoShow: true,
          ImmersiveVideoProps: { ...formData?.postSuccessMessage },
        })
      )
      const accessedVideo = getLocalStorageItem('__accessedVideo') || {}
      const videoId = formData?.postSuccessMessage?.sys.id
      if (videoId) {
        accessedVideo[videoId] = true
        setLocalStorageItem('__accessedVideo', accessedVideo)
      }
    }
    store.dispatch(deleteFormActivation({ contentfulFormId: contentfulId }))
    store.dispatch(setProgress({ status: 'SUCCESS' }))
  }, 3000)*/
}

export function uet_report_conversion() {
  if (isBrowser()) {
    window.uetq = window?.uetq || []
    window.uetq.push('event', 'submit_lead_form', {})
  }
}

export function GA4FormCloseTracking(formId: string) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'custom_form_floating_close',
      formId: formId,
    })
  }
}

export function formStartTracking(formId: string) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'custom_form_start',
      formId: formId,
    })
  }
}

export function postToReonomy({
  values,
  setFormSubmitionState,
  setFormSubmitionStateErrorMsg,
  sfmcUrl,
  endpointUrl,
  contentfulId,
  isBingEnabled = false,
  formCategory,
  formData,
}) {
  try {
    let reqHeaders = new Headers()
    reqHeaders.append('Content-Type', 'application/json')

    let reqBody = JSON.stringify({
      email: values?.email,
      first_name: values?.first_name,
      last_name: values?.last_name,
      password: values?.password,
      remote_addr: '0.0.0.0',
      phone: values?.phone,
      accept_agreements: ['466c3907-63ee-49fd-b2b2-a225c5765462'],
    })

    let requestOptions = {
      method: 'POST',
      headers: reqHeaders,
      body: reqBody,
      redirect: 'follow',
    }
    fetch('/api/form/reonomy-signup', requestOptions)
      .then((response) => response.json())
      .then((response) => {
        if (
          response?.data?.auth &&
          response?.data?.otk &&
          response?.data?.user_id
        ) {
          setFormSubmitionStateErrorMsg('')

          postToPardot({
            values,
            setFormSubmitionState,
            sfmcUrl,
            endpointUrl,
            contentfulId,
            formData,
          })

          if (isBingEnabled) {
            uet_report_conversion()
          }
        } else {
          let error = ''

          if (response?.data?.code === 'PHONE_ALREADY_IN_USE') {
            error = 'This phone number is already in use.'
          } else if (response?.data?.code === 'DUPLICATE_ERROR') {
            error =
              'There is already an account associated with this email address.'
          }
          if (error) {
            setFormSubmitionStateErrorMsg({
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  attrs: {
                    textAlign: 'left',
                  },
                  content: [
                    {
                      text: error,
                      type: 'text',
                    },
                  ],
                },
              ],
            })
          }

          setFormSubmitionState('FAILED')
          formErrorEvent({ contentfulId, formCategory })
        }
      })
      .catch((error) => {
        console.error(
          'error: could not get response of form submission: ',
          error
        )
        setFormSubmitionState('FAILED')
        formErrorEvent({ contentfulId, formCategory })
      })
  } catch (error) {
    setFormSubmitionState('FAILED')
    formErrorEvent({ contentfulId, formCategory })
  }
}

const checkEmailForReonomy = async (email: string) => {
  try {
    const visageApiValidateEmail = '/api/form/reonomy-email'

    let reqHeaders = new Headers()
    reqHeaders.append('Content-Type', 'application/json')

    let reqBody = JSON.stringify({
      email: email,
    })

    let requestOptions = {
      method: 'POST',
      headers: reqHeaders,
      body: reqBody,
      redirect: 'follow',
    }
    let response = await fetch(visageApiValidateEmail, requestOptions)
    response = await response.json()
    if (
      response?.status === 'success' &&
      (response?.data?.is_generic_email ||
        response?.data?.code === 'EMAIL_IS_GENERIC')
    ) {
      return 'Generic emails like Gmail or Yahoo not accepted'
    } else {
      if (response?.data?.is_generic_email === false) {
        return null
      }
      return null
    }
  } catch (error) {
    console.warn('errror', error)
    return 'Error validating email'
  }
}

const checkGenericEmail = async (email: string) => {
  if (!email) return null
  try {
    let reqHeaders = new Headers()
    reqHeaders.append('Content-Type', 'application/json')

    let reqBody = JSON.stringify({
      email: email,
    })

    let requestOptions = {
      method: 'POST',
      headers: reqHeaders,
      body: reqBody,
      redirect: 'follow',
    }
    let response = await fetch('/api/form/validate-email', requestOptions)
    response = await response.json()
    if (response?.isSuccess) {
      if (response?.isGenericEmail) {
        return 'Generic emails like Gmail or Yahoo not accepted'
      } else {
        return null
      }
    } else {
      return 'Error validating email'
    }
  } catch (error) {
    console.warn('errror', error)
    return 'Error validating email'
  }
}

export const generateEmailValidatioMsg = async ({
  inputValue,
  validationErrorMessage,
  isAltusEmailAllowed,
  isGenericEmailAllowed,
  formTemplate,
}: IEmailValidationMsg): Promise<string | null> => {
  // Example definitions for AltusEmails and GenericEmails arrays
  // const AltusEmails: string[] = ["altus.com", "altusgroup.com"];
  // const GenericEmails: string[] = ["gmail.com", "yahoo.com"];

  if (formTemplate === 'ReonomyFreeTrial') {
    return await checkEmailForReonomy(inputValue)
  }

  let notAllowedEmails: string[] = []

  if (!isAltusEmailAllowed) {
    notAllowedEmails = [...notAllowedEmails, ...AltusEmails]
  }

  // Construct the notIncludeEmailsRegex string if there are not allowed emails
  const notIncludeEmailsRegex = notAllowedEmails.length
    ? new RegExp(`^(?!.*@(${notAllowedEmails.join('|')})).*$`)
    : null

  // Define the generic email regex
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

  // Validate the email format
  if (!emailRegex.test(inputValue)) {
    return validationErrorMessage
  }

  // Validate against not allowed email patterns
  if (notIncludeEmailsRegex && !notIncludeEmailsRegex.test(inputValue)) {
    return 'Please request access from the corporate marketing team, or contact <NAME_EMAIL>'
  }

  if (!isGenericEmailAllowed) {
    return await checkGenericEmail(inputValue)
  }

  // If everything is valid, return null
  return null
}

export const checkAndAddSFIDToFormProps = (fieldValues: any) => {
  let params = getQueryParamJson()

  const sfidParams = params?.['sfid']

  for (const fieldValue in fieldValues) {
    if (fieldValue === 'Integration_Primary_Campaign' && sfidParams) {
      fieldValues['Integration_Primary_Campaign'] = sfidParams
    }
  }

  return fieldValues
}

function emailTracking(email: string) {
  if (!email || !isProduction()) return
  if (isBrowser()) {
    console.log('Email=' + email)
    window._etmc.push(['setOrgId', '546007750'])
    window._etmc.push(['setUserInfo', { email: email }])
    window._etmc.push(['trackPageView'])
  }
}

export function getHiddenFieldsObj(contentfulId: string) {
  if (!isBrowser()) return {}
  const form = document.getElementById(
    `form-${contentfulId}`
  ) as HTMLFormElement
  const hiddenFields = Array.from(
    form?.querySelectorAll('input[type="hidden"]') ?? []
  ) as HTMLInputElement[]
  let hiddenFieldsValues: any = {}
  for (const field of hiddenFields) {
    hiddenFieldsValues[field?.name] = field?.value
  }
  return hiddenFieldsValues
}

interface IFieldsTobeAppended {
  altusFieldName: string
  altusLabel: string
}

export function findFieldNamesWithAppendInComment(obj: {
  [k: string]: any
}): IFieldsTobeAppended[] {
  let fieldNames: IFieldsTobeAppended[] = []

  for (const key in obj) {
    if (Array.isArray(obj[key])) {
      // If the current value is an array, iterate through each element
      for (const item of obj[key]) {
        if (typeof item === 'object' && item !== null) {
          // Recursively search in the array's elements if they are objects
          fieldNames = fieldNames.concat(
            findFieldNamesWithAppendInComment(item)
          )
        }
      }
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      // If the current value is an object, check for appendValueInComment
      if (obj?.[key]?.appendValueInComment === true) {
        fieldNames.push({
          altusFieldName: obj?.[key]?.altusFieldName,
          altusLabel: obj?.[key]?.altusLabel,
        })
      }
      // Recursively search nested objects
      fieldNames = fieldNames.concat(
        findFieldNamesWithAppendInComment(obj[key])
      )
    }
  }

  return fieldNames
}

export function appendInComments(
  formValues: { [k: string]: any },
  fieldsTobeAppended: IFieldsTobeAppended[]
): Record<string, string> {
  if (!fieldsTobeAppended.length) return formValues

  let comment = `${formValues['comments'] || ''} \n
      The prospect has also provided the following information:
      \n`

  for (const field of fieldsTobeAppended) {
    if (formValues[field.altusFieldName]) {
      comment += ` ${field.altusLabel} : ${formValues[field.altusFieldName]},`
      delete formValues[field.altusFieldName]
    }
  }

  return {
    ...formValues,
    comments: comment.trim(),
  }
}

export function getDependantFieldNames(obj, fieldNames = []) {
  if (!obj) return []

  // Check if the current object has __typename of "FormField" and altusFieldName exists
  if (obj.__typename === 'FormField' && obj.altusFieldName) {
    fieldNames.push(obj?.altusFieldName)
  }

  // Recursively check any nested collections or items
  for (let key in obj) {
    if (obj[key] && typeof obj[key] === 'object') {
      getDependantFieldNames(obj[key], fieldNames)
    }
  }

  return fieldNames
}

export function deleteDependantFields(formValues, dependantNames) {
  const updatedValues = { ...(formValues ?? {}) }

  dependantNames?.forEach((fieldName) => {
    if (Object.hasOwn(updatedValues, fieldName)) {
      delete updatedValues[fieldName]
    }
  })

  return updatedValues
}

export async function processFormSubmission({
  contentfulFormId,
  formData,
  setFormSubmitionState,
  values = {},
  hiddenFields = [],
  setFormSubmitionStateErrorMsg = () => { },
  section = [],
}: {
  contentfulFormId: string
  formData?: Record<string, any>
  setFormSubmitionState: any
  values: Record<string, string>
  hiddenFields?: Record<string, any>[]
  setFormSubmitionStateErrorMsg?: Function
  section?: any[]
}) {
  // Retrieve and parse values stored in the session storage under the key '__params'.
  const urlParamsValues = JSON.parse(getSessionStorageItem('__params') ?? '{}')

  let hiddenFieldsFromUI = {}
  if (section?.length > 0) {
    // If the form has sections, get the hidden fields from each section.
    section.forEach((_: any, index: number) => {
      const sectionHiddenFields = getHiddenFieldsObj(
        `${contentfulFormId}-section-${index}`
      )
      Object.assign(hiddenFieldsFromUI, sectionHiddenFields)
    })
  } else {
    // If the form does not have sections, get the hidden fields from the main form.
    hiddenFieldsFromUI = getHiddenFieldsObj(contentfulFormId)
  }

  const fieldsTobeAppended = findFieldNamesWithAppendInComment([
    ...(formData?.formFieldsCollection?.items ?? []),
    ...hiddenFields,
  ])

  // FOR geo location based agreement link
  let agreementId
  if (formData?.isDynamicAgreement) {
    try {
      const storeData = store.getState()
      const countryCode = storeData?.app?.geoLocationData?.countryCode
      const businessRegion = storeData?.app?.geoLocationData?.businessRegion

      agreementId = await getfileIdByMcaUrl(getAgreementLinkByCountry({
        countryCode: countryCode,
        businessRegion: businessRegion,
      }))

      if (!agreementId) {
        console.error('agreementId not found')
        setFormSubmitionState('FAILED')
        return
      }
    } catch (error) {
      setFormSubmitionState('FAILED')
      console.error('Error fetching agreementId:', error)
      return
    }
  }

  const ga4ClientId = getClientIdFromCookie()
  const measurementId = getMeasurementId()

  let formValues = {
    utm_campaign: 'none',
    utm_content: 'none',
    utm_term: 'none',
    utm_medium: 'website-direct',
    utm_source: 'none',
    ...hiddenFieldsFromUI,
    ...urlParamsValues,
    ...values,
    // Add the source URL from the browser's window location if in a browser context.
    sourceUrl: isBrowser() && window?.location?.href,
    // Add the form ID from the updatedProps data.
    formid: contentfulFormId,
    agreementid: agreementId,
    clientid: ga4ClientId,
    measurementid: measurementId,
  }

  formValues = appendInComments(formValues, fieldsTobeAppended)
  formValues = checkAndAddSFIDToFormProps(formValues)

  // Set the form submission state to "LOADING" when the form is being submitted.
  setFormSubmitionState('LOADING')

  if (formData?.template === 'ReonomyFreeTrial') {
    postToReonomy({
      values: formValues,
      setFormSubmitionState,
      setFormSubmitionStateErrorMsg,
      sfmcUrl: formData?.sfmcUrl,
      endpointUrl: formData?.endpointUrl,
      contentfulId: contentfulFormId,
      formCategory: formData?.formCategory,
      formData,
    })
  } else {
    postToPardot({
      values: formValues, // Pass the combined form values.
      setFormSubmitionState, // Pass the function to set the form submission state.
      sfmcUrl: formData?.sfmcUrl, // Pass the endpoint URL from updatedProps data.
      endpointUrl: formData?.endpointUrl, // Pass the endpoint URL from updatedProps data.
      contentfulId: contentfulFormId, // Pass the Contentful ID from updatedProps.
      formCategory: formData?.formCategory, // Pass the form category from updatedProps data.
      formData,
    })
  }
}

export const getAgreementLinkByCountry = ({
  countryCode,
  businessRegion
}: {
  countryCode?: string
  businessRegion?: string
}) => {
  // Convert country code to uppercase for consistent comparison
  const upperCountry = countryCode?.toUpperCase()

  // Check if country is in Americas (US or Canada)
  const isAmer = ['US', 'CA'].includes(upperCountry ?? '')
  // Check if country is in Australia/New Zealand
  const isAnz = ['AU', 'NZ'].includes(upperCountry ?? '')
  // Check if region is EMEA or APAC (excluding ANZ countries)
  const isEMEAorAPACExclAnz = (businessRegion === 'EMEA' || businessRegion === 'APAC') && !isAnz

  // Return appropriate MCA link based on region
  if (isAmer) return MCA_LINKS.USA
  if (isAnz) return MCA_LINKS.AU
  if (isEMEAorAPACExclAnz) return MCA_LINKS.UK

  return MCA_LINKS.USA // default fallback USA
}

export const getfileIdByMcaUrl = async (mcaUrl: string): Promise<string | null> => {
  if (!mcaUrl) return null
  const fileId = await getAgreementDetail(mcaUrl)
  return fileId
}
