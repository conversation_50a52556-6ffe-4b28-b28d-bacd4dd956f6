import SimpleButtonWIcon from '../../../components/CTAs/SimpleButtonWIcon'
import { setSessionStorageItem } from '../../../globals/utils'
import { getPrimaryLinkProps } from '../../../lib/propsMapping/link.mapping'
import { notIncludeEmails } from '../Form/notIncludeEmails'
import { generateEmailValidatioMsg } from '../Form/utils'
import styles from './index.module.scss'

const FormHeroInputType = ({
    input,
    isLightMode,
    mainHandleChange,
    values
}: any) => {
    const inputClass = isLightMode ? styles.inputCp1 : styles.inputCs2
    const lableClass = isLightMode ? styles.lable : styles.drkLable
    if (
        input?.hasOwnProperty('__typename') &&
        input?.__typename === 'LinkComponent'
    ) {
        const linkProps = getPrimaryLinkProps(input)
        // debugger
        return (
            <SimpleButtonWIcon
                {...linkProps}
                isButton
                isFormButton
                htmlAttr={{
                    className: styles.heroFormBtn,
                    type: 'submit',
                    onClick: () => {
                        setSessionStorageItem('Email', values['Email'] || '')
                    }
                }}
                isIconPrefixed={input?.iconPlacement === 'Prefix'}
                textContent={input?.text ?? 'Submit'}
                icon={input?.icon ?? ''}
                variant="primary"
                isChevron2Arrow={input?.isChevron2Arrow}
                isLightMode={isLightMode}
                actionContent={input?.actionContent}
            />
        )
    }

    if (input.altusFieldType === 'Text' || input.altusFieldType === 'Hidden')
        return (
            <div
                className={styles.inputDiv}
                style={{
                    display: input.altusFieldType === 'Hidden' ? 'none' : 'block'
                }}
            >
                <label
                    htmlFor={input?.altusFieldName}
                    className={`form-label ${lableClass} fs2 `}
                >
                    {input?.altusLabel}
                </label>
                <input
                    type="text"
                    name={input?.altusFieldName}
                    className={`${styles.floatingFormEmail} ${inputClass}`}
                    id={input?.altusFieldName}
                    pattern={generateEmailValidatioMsg(notIncludeEmails)}
                    placeholder={input?.placeholderText}
                    required={input?.altusIsRequired}
                    onChange={(event) =>
                        mainHandleChange(event.target.value, input?.altusFieldName)
                    }
                />
                <div className="invalid-feedback">{input?.validationErrorMessage}</div>
            </div>
        )

    if (input.altusFieldType === 'Email') {
        return (
            <div className={styles.inputDiv}>
                <input
                    type="email"
                    name={input?.altusFieldName}
                    className={`${styles.FormEmail} ${inputClass}`}
                    id={input?.altusFieldName}
                    pattern={generateEmailValidatioMsg(notIncludeEmails)}
                    placeholder={input?.placeholderText}
                    required={input?.altusIsRequired}
                    onChange={(event) => mainHandleChange(event.target.value, 'Email')}
                />
                <div className="invalid-feedback">{input?.validationErrorMessage}</div>
            </div>
        )
    }
}

export default FormHeroInputType