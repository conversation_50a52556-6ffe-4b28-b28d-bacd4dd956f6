//Default SCSS variables are prepended in next.config.js

.formFloatingRoot {
  min-height: 250px;
  width: 100%;
  position: relative;

  .blurContainer {
    .formFloatingContainer {
      width: 100%;
      height: 100%;
      z-index: 3;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 60px 0 60px;
      // backdrop-filter: blur(1rem);

      .contentContainer {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 30px;
        justify-content: space-between;
        width: 100%;

        .toasts {
          width: 100%;
        }

        .formFloatingInputDiv {
          display: flex;
          align-items: flex-start;
          flex-direction: column;
          gap: 20px;
          width: 70%;

          & > input {
            margin-bottom: 0;
          }

          & > button {
            align-self: start;
            margin-top: 0;
            margin-bottom: 0;
          }
        }

        @media screen and (max-width: $mScreenSize) {
          row-gap: 30px;
          padding: 30px 0;
        }
      }

      @media screen and (max-width: $mScreenSize) {
        padding: 20px 0;
      }
    }
  }
}

.floatingFormEmail {
  width: 90%;
  height: 40px;
  border: none;
  outline: 0;
  font-size: $fs3;
  font-family: $fSansReg;
  // color: $cs2;
  background: transparent;
  transition: border-color 0.2s;

  /* Styling for the input element's placeholder */
  &::placeholder {
    color: inherit;
    font-family: $fSansLight;
    font-size: $fs3;
  }

  &:focus::placeholder {
    color: $cn3;
  }

  /* Styling when the placeholder is shown and the input has no value */
  &:placeholder-shown ~ .formLabel {
    font-size: $fs4;
    cursor: text;
    top: 30px;
    color: $cn2;
  }

  /* Styling for the label when the input is focused */
  &:focus ~ .formLabel {
    position: absolute;
    top: 0px;
    display: flex;
    align-items: center;
    transition: 0.2s;
    color: $cn3;
    font-size: $fs2;
  }
}

.leftSection {
  width: 100%;
}

@media screen and (max-width: $mScreenSize) {
  .formFloatingInputDiv {
    width: 90% !important;
  }

  .leftSection {
    width: 100%;
  }

  .formFloatingContainer {
    padding: 30px;
  }

  .toasts {
    width: 100% !important;
  }
}

@media screen and (max-width: $smScreenSize) {
  .formFloatingInputDiv {
    width: 100% !important;
  }
}

.fromCon {
  width: 100%;
}

.inputCp1 {
  color: $cp1;
  border-bottom: 2px solid $cp1;
}

.inputCs2 {
  color: $cs2;
  border-bottom: 2px solid $cs2;
}

.lable {
  color: $cp1;
}

.drkLable {
  color: $cs2;
}

.inputDiv {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.closeButton {
  position: absolute;
  top: 30px;
  right: 4%;
  z-index: 1;
}
