'use client'
import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import CardFeaturedAfsInsightsListing from '../../../../components/Cards/CardFeatured/CardFeaturedAfsInsightsListing'
import LayoutContainer from '../../../../components/Containers/LayoutContainer'
import SystemFiltration from '../../../../components/SystemFilteration'
import { SystemFiltrationI } from '../../../../components/SystemFilteration/interface'
import { getLocalStorageItem, useTranslation } from '../../../../globals/utils'
import {
  resetFilters,
  setFiltersState,
} from '../../../../redux/slices/filterSlice/filtersSlice'
import { setTagsMap } from '../../../../redux/slices/tagMapSlice'
import {
  getFiltersObject,
  queryStringToObject,
} from '../../lib/copyFilters/parseFiltersLink'
import { filterItems } from '../../lib/filters'
import { getSystemFilterationTabsProps } from '../utils'
import { getCustomerStoriesListingProps } from './customerStories.mapping'

const AFSCustomerEvents = (props: any) => {
  const filterType = props.afsFilterData.template
    .toLowerCase()
    .replace(/\s/g, '')
  const locale = props?.locale || getLocalStorageItem('locale')
  const afsFilterCollection = props.afsFilterData.afsFiltersCollection
  const [mappedAfsPages, setMappedAfsPages] = useState<any>([]) // state that will hold the mapped data
  const [systemFiltrationProps, setSystemFiltrationProps] =
    useState<SystemFiltrationI>() // state that will hold the system filteration props

  const dispatch = useDispatch()

  const foundStr = useTranslation('found', locale)

  const resultStr = useTranslation('results', locale)

  const filters = useSelector((state: any) => state?.filters[filterType])

  const [afsCardFeaturedAfsListingProps, setCardFeaturedAfsListingProps] =
    React.useState()

  // Effect that will run when the component mounts
  React.useEffect(() => {
    const queryString = window.location.search // get query string from url

    // get mapped data and store it in mappedAfsPages state
    const { res: mappedPageData, usedTags } = getCustomerStoriesListingProps(
      props.afsPageData,
      true,
      locale
    )
    setMappedAfsPages(mappedPageData)

    // set tags map in redux
    dispatch(
      setTagsMap({
        field: `${filterType}TagsMap`,
        value: Object.fromEntries(usedTags),
      })
    )

    // get system filteration tabs props
    const filtersData: SystemFiltrationI = {
      tabs: getSystemFilterationTabsProps(
        afsFilterCollection,
        filterType,
        usedTags
      ),
      filterType,
    }

    // set system filteration tabs props
    setSystemFiltrationProps({ ...filtersData, locale })

    // if query string exists, parse it and set filters in redux state
    if (queryString) {
      const queryObject = queryStringToObject(queryString) // parse query string
      const filtersObject = getFiltersObject(queryObject) // parsed query string to filters object of specific type or format
      delete filtersObject.lang

      // set filters in redux
      dispatch(
        setFiltersState({
          filterType,
          filterState: filtersObject,
        })
      )

      // filter items based on filters
      const cards = filterItems(mappedAfsPages.cards, filters)
      const data = {
        cards,
        results: {
          textContent: `${cards?.length} ${resultStr} ${foundStr}`,
        },
        isFeaturedActive: false,
      }
      setCardFeaturedAfsListingProps(data)
    } else {
      // simply set mappedAfsPages state
      dispatch(resetFilters({ filterType: filterType }))
      setCardFeaturedAfsListingProps(mappedPageData)
    }
  }, [props.afsPageData])

  // Effect that will every time filters state changes and will filter items
  React.useEffect(() => {
    if (filters) {
      const cards = filterItems(mappedAfsPages.cards, filters)
      const data = {
        cards,
        results: {
          textContent: `${cards?.length} ${resultStr} ${foundStr}`,
        },
        isFeaturedActive: false,
      }
      setCardFeaturedAfsListingProps(data)
    }
  }, [filters])

  return (
    <>
      {systemFiltrationProps && <SystemFiltration {...systemFiltrationProps} />}
      <LayoutContainer>
        {afsCardFeaturedAfsListingProps && (
          <CardFeaturedAfsInsightsListing
            {...afsCardFeaturedAfsListingProps}
            locale={locale}
            filterType={filterType}
          />
        )}
      </LayoutContainer>
    </>
  )
}

export default AFSCustomerEvents
