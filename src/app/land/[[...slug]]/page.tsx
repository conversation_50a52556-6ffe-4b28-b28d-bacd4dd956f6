import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { ErrorBoundary } from 'react-error-boundary'
import TheError from '../../../components/Kernel/@core/TheError/TheError'
import {
  DYNAMIC_PAGE_PREFIX,
  EXCLUDED_KEYS,
  LOCALE_CONSTANT,
} from '../../../globals/utils'
import { BUISNESS_DOMAINS } from '../../../utils'
import PageRoot from '../../../utils/PageRoot'
import {
  fetchPageIdFromSlug,
  metadataProcessor,
  staticParamsProcessor,
} from '../../../utils/pageUtils'

// export const dynamicParams = false // true | false,
// export const revalidate = 3600 // seconds

// MetaData
export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const newParam = JSON.parse(JSON.stringify(params))
  if (newParam?.slug && Array.isArray(newParam?.slug)) {
    newParam.slug.unshift(DYNAMIC_PAGE_PREFIX)
  }
  return await metadataProcessor(newParam)
}

/**
 * @param params - The parameters from the URL, specifically the slug.
 * @param searchParams - The search parameters from the URL.
 * @returns
 */
async function Page({ params, searchParams }: PageProps) {
  let pageId = null
  const newParam = JSON.parse(JSON.stringify(params))
  if (newParam?.slug && Array.isArray(newParam?.slug)) {
    newParam.slug.unshift(DYNAMIC_PAGE_PREFIX)
  }
  const sortingParams = Object.entries(searchParams ?? {}).filter(
    ([key]) => !EXCLUDED_KEYS.includes(key)
  )

  if (sortingParams.length) {
    console.log('Unexpected search params:', Object.fromEntries(sortingParams))
  }
  try {
    let locale = LOCALE_CONSTANT[newParam.slug?.[0]] || 'en-CA'
    if (BUISNESS_DOMAINS['altus'] === process.env.NEXT_PUBLIC_DOMAIN) {
      if (newParam?.slug?.[newParam?.slug?.length - 1] === 'fr') {
        newParam.slug?.pop()
        locale = LOCALE_CONSTANT['fr']
      }
    }
    const fullUrl = newParam.slug?.join('/') || '/'
    pageId = await fetchPageIdFromSlug(fullUrl, locale)
    console.log(pageId, 'pageId')
  } catch (error) {
    return <TheError error={{}} />
  }
  if (!pageId) {
    return notFound()
  }
  const sortingConfig = Object.fromEntries(sortingParams)

  return (
    <ErrorBoundary FallbackComponent={TheError}>
      <PageRoot
        params={newParam}
        pageId={pageId}
        sortingConfig={sortingConfig}
      />
    </ErrorBoundary>
  )
}

// Export the component as a Next.js server component
export default Page
/**
 *
 * @returns Promise<{ slug: string[] }[]>
 * This function generates static parameters for the dynamic route.
 * It fetches the page IDs from the database and returns them as an array of objects.
 */
export async function generateStaticParams() {
  return await staticParamsProcessor()
}
