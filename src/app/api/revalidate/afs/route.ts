import { NextRequest } from 'next/server'

type cfTag = {
  sys: {
    id: string
  }
}

export async function POST(request: NextRequest) {
  const req = await request.json()

  const domainTag = req.metadata.tags.find((tag: cfTag) =>
    tag.sys.id.startsWith('domain')
  )?.sys?.id
  const afsTag = req.metadata.tags.find((tag: cfTag) =>
    tag.sys.id.startsWith('afs')
  )?.sys?.id
  console.log('POST: revalidate afs', domainTag, afsTag)

  if (!afsTag) {
    return Response.json({
      revalidated: false,
      now: Date.now(),
      message: 'Missing afs tag to revalidate',
    })
  }

  const domainVercelUrl = getVercelUrl(domainTag)

  if (domainVercelUrl) {
    const afsSlug = getAFSslugFromTag(afsTag, domainTag)
    if (afsSlug?.length > 0) {
      const revalidateResponse = await fetch(
        `${domainVercelUrl}/api/revalidate/page/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ slugs: afsSlug }),
        }
      ).then((res) => res.json())

      return Response.json(revalidateResponse)
    } else {
      return Response.json({
        revalidated: false,
        now: Date.now(),
        message: 'AFS tag not found or not supported',
      })
    }
  } else {
    return Response.json({
      revalidated: false,
      now: Date.now(),
      message: 'Domain not found or not supported',
    })
  }
}

function getAFSslugFromTag(afsTag: string, domainTag: string): string[] {
  switch (domainTag) {
    case 'domainAltusGroupCom':
      const tagToslug: Record<string, string[]> = {
        afsInsights: [
          '/insights/all',
          '/insights/all/',
          '/insights/all/fr',
          '/insights/all/fr/',
        ],
        afsPressRelease: [
          '/press-releases',
          '/press-releases/',
          '/press-releases/fr',
          '/press-releases/fr/',
        ],
        afsEvents: ['/events', '/events/', '/events/fr', '/events/fr/'],
      }
      return tagToslug[afsTag]
    case 'domainReonomyCom':
      return []
    case 'domainFinanceActiveCom':
      return [
        '/resources',
        '/resources/',
        '/fr/ressources',
        '/fr/ressources/',
        '/de/ressourcen',
        '/de/ressourcen/',
        '/it/risorse',
        '/it/risorse/',
        '/es/recursos',
        '/es/recursos/',
        '/nl/media',
        '/nl/media/',
      ]
    default:
      return []
  }
}

function getVercelUrl(domainTag: string) {
  const domainToUrl: Record<string, string> = {
    domainAltusGroupCom: 'https://msa-agl-v3w-git-staging-altus.vercel.app',
    domainReonomyCom: 'https://msa-reo-v3w-git-staging-altus.vercel.app',
    domainFinanceActiveCom: 'https://msa-fia-v3w-git-staging-altus.vercel.app',
  }
  return domainToUrl[domainTag]
}
