import { revalidatePath } from 'next/cache';
import { NextRequest, NextResponse } from 'next/server';
import { corsAllowedOrigins, corsAllowedPattern } from '../../../../globals/utils';

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin') ?? ''
  const response = new NextResponse(null, { status: 200 })
  // Set CORS headers if the origin is allowed
  if (corsAllowedPattern.test(origin) || corsAllowedOrigins.includes(origin)) {
    console.log('Setting CORS headers for origin in OPTIONS: ', origin)
    response.headers.append('Access-Control-Allow-Origin', origin)
  }
  response.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', '*')
  return response
}

export async function POST(request: NextRequest) {
  const req = await request.json()
  let slugs = req?.slugs

  console.log('POST: req body', req)

  if (slugs?.length > 0) {
    slugs.forEach((slug: string) => {
      revalidatePath(slug)
    })
    return Response.json({ revalidated: true, now: Date.now(), req })
  }

  return Response.json({
    revalidated: false,
    now: Date.now(),
    message: 'Missing slug to revalidate',
  })
}
