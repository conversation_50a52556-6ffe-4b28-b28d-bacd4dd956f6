/**
 * 404 Error Page Data Fetcher
 *
 * Server-side function that fetches all necessary data for rendering 404 error pages
 * across multiple locales. This function is used by Next.js not-found.tsx to provide
 * complete page data including navigation, footer, and error content.
 *
 * Key Features:
 * - Multi-locale support for internationalized 404 pages
 * - Fetches navigation header, footer, and error page content from Contentful
 * - Processes linked objects and references for complete data structure
 * - Provides consistent error page experience across all supported locales
 *
 * Data Sources:
 * - Navigation header from Contentful (componentNavigationHeaderCollection)
 * - Navigation footer from Contentful (componentNavigationFooterCollection)
 * - Error page content from Contentful (componentErrorPageCollection)
 *
 * @returns Promise<{navigationData, footerData, error404Data}> Complete 404 page data
 */
'use server'

import { BUISNESS_DOMAINS_LOCALE_NAVIGATION } from '../globals/utils'
import { fetchGraphQL } from '../lib/api'
import { findProcessAndPlaceObjects } from '../lib/propsMapping/index.mapping'
import { errorQuery } from '../lib/queries/error.query'
import { navigationFooterQuery } from '../lib/queries/navigationFooter.query'
import { navigationPrimaryHeaderQuery } from '../lib/queries/navigationHeader.query'

/**
 * Fetches comprehensive 404 error page data for all supported locales
 *
 * This function orchestrates the data fetching process for 404 error pages by:
 * 1. Determining supported locales based on the current domain
 * 2. Fetching navigation header data for each locale
 * 3. Fetching navigation footer data for each locale
 * 4. Fetching error page content data for each locale
 * 5. Processing all linked objects and references
 * 6. Organizing data by locale for easy consumption
 *
 * The function ensures that 404 pages have complete navigation context and
 * localized error content, maintaining consistent user experience even when
 * users encounter missing pages.
 *
 * @returns {Promise<Object>} Object containing:
 *   - navigationData: Header navigation data indexed by locale
 *   - footerData: Footer navigation data indexed by locale
 *   - error404Data: Error page content data indexed by locale
 */
export async function get404Data() {
  // Initialize data containers for each content type
  // These will be populated with locale-specific data
  let navigationData = {}
  let footerData = {}
  let error404Data = {}

  // Get the list of supported locales for the current domain
  // This ensures 404 pages are available in all supported languages
  const locales =
    BUISNESS_DOMAINS_LOCALE_NAVIGATION[process.env.NEXT_PUBLIC_DOMAIN]

  // Process each supported locale to build complete 404 page data
  for (let locale of locales) {
    /**
     * Navigation Header Data Fetching
     *
     * Fetches the primary navigation header for the current locale.
     * This ensures 404 pages maintain consistent navigation structure
     * and users can easily navigate back to valid pages.
     */
    let rawResponse = await fetchGraphQL(
      navigationPrimaryHeaderQuery(locale),
      true, // Enable preview mode for latest content
      'header' // Cache tag for selective revalidation
    )
    let navResponse =
      rawResponse?.data?.componentNavigationHeaderCollection?.items?.at(0)

    // Process linked objects within navigation data
    // This resolves references to other Contentful entries
    await findProcessAndPlaceObjects({
      obj: navResponse,
      visitedIds: ['not-found'], // Track visited IDs to prevent circular references
    })
    navigationData = { ...navigationData, [locale]: navResponse }

    /**
     * Navigation Footer Data Fetching
     *
     * Fetches the navigation footer for the current locale.
     * Footer provides additional navigation options and important links
     * that help users find relevant content when they encounter 404 errors.
     */
    let rawFooterResponse = await fetchGraphQL(
      navigationFooterQuery(locale),
      true, // Enable preview mode for latest content
      'footer' // Cache tag for selective revalidation
    )
    let footerResponse =
      rawFooterResponse?.data?.componentNavigationFooterCollection?.items?.at(0)

    // Process linked objects within footer data
    // This resolves references to other Contentful entries
    await findProcessAndPlaceObjects({
      obj: footerResponse,
      visitedIds: ['footer'], // Track visited IDs to prevent circular references
    })
    footerData = { ...footerData, [locale]: footerResponse }

    /**
     * Error Page Content Data Fetching
     *
     * Fetches the 404 error page content for the current locale.
     * This includes error messaging, helpful links, search functionality,
     * and any other content designed to assist users who encounter missing pages.
     */
    let rawError404Response = await fetchGraphQL(
      errorQuery(locale),
      true, // Enable preview mode for latest content
      '404' // Cache tag for selective revalidation
    )
    let error404Response =
      rawError404Response?.data?.componentErrorPageCollection?.items?.at(0)

    // Process linked objects within error page data
    // This resolves references to other Contentful entries like menu lists and buttons
    await findProcessAndPlaceObjects({
      obj: error404Response,
      visitedIds: ['error'], // Track visited IDs to prevent circular references
    })

    // Store processed error page data indexed by locale
    error404Data = { ...error404Data, [locale]: error404Response }
  }

  // Return complete 404 page data structure
  // Each data type is organized by locale for easy access in components
  return { navigationData, footerData, error404Data }
}
