import fs from 'fs'
import type { NextApiRequest, NextApiResponse } from 'next'
import path from 'path'
import { corsAllowedOrigins, corsAllowedPattern } from '../../globals/utils'

const handler = async (
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> => {
  const origin = req.headers.origin || ''
  // Set CORS headers if the origin is allowed
  if (corsAllowedPattern.test(origin) || corsAllowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin)
  }
  res.setHeader('Access-Control-Allow-Credentials', 'true')
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS')
  res.setHeader(
    'Access-Control-Allow-Headers',
    'Authorization, Accept, Content-Type, Origin, User-Agent, Cache-Control, X-Requested-With,  x-vercel-protection-bypass'
  )

  if (req.method === 'OPTIONS') {
    // Pre-flight request. Reply successfully:
    res.status(200).json({ error: 'Method Not Allowed' })
    return
  }

  if (req.method === 'POST') {
    try {
      const { content } = req.body

      if (!content) {
        return res.status(400).json({ error: 'Content is required' })
      }

      const filePath = path.join(process.cwd(), 'public', 'robots.txt')

      fs.writeFileSync(filePath, content, 'utf8')
      return res
        .status(200)
        .json({ message: 'robots.txt updated successfully' })
    } catch (error) {
      console.error('Error writing robots.txt:', error)
      return res.status(500).json({ error: 'Failed to update robots.txt' })
    }
  } else {
    return res.status(405).json({ message: 'Method Not Allowed' })
  }
}

export default handler
