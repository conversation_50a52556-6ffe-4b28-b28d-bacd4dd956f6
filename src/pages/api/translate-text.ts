// pages/api/translate-text.ts
import { TranslationServiceClient } from '@google-cloud/translate'
import { NextApiRequest, NextApiResponse } from 'next'
import { corsAllowedOrigins, corsAllowedPattern, l } from '../../globals/utils'
import serviceAccountKey from '../../utils/altus-group-maps-da212ff0d17d.json'

interface TranslationRequestBody {
  target_language: string
  source_language: string
  text_array: string[]
}

interface TranslationResponse {
  text: string
  translatedText: string
}

const translationClient = new TranslationServiceClient({
  credentials: serviceAccountKey,
})

const glossaryPath = {
  fr: 'projects/altus-group-maps/locations/us-central1/glossaries/Website-EN-FR-Glossary_v3',
  de: 'projects/altus-group-maps/locations/us-central1/glossaries/Website-EN-DE-Glossary_v3',
  it: 'projects/altus-group-maps/locations/us-central1/glossaries/Website-EN-IT-Glossary_v3',
  es: 'projects/altus-group-maps/locations/us-central1/glossaries/Website-EN-ES-Glossary_v3',
  nl: 'projects/altus-group-maps/locations/us-central1/glossaries/Website-EN-NL-Glossary_v3',
}

const handler = async (
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> => {
  const origin = req.headers.origin || ''
  // Set CORS headers if the origin is allowed
  if (corsAllowedPattern.test(origin) || corsAllowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin)
  }
  res.setHeader('Access-Control-Allow-Credentials', 'true')
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS')
  res.setHeader(
    'Access-Control-Allow-Headers',
    'Authorization, Accept, Content-Type, Origin, User-Agent, Cache-Control, X-Requested-With,  x-vercel-protection-bypass'
  )

  if (req.method === 'OPTIONS') {
    // Pre-flight request. Reply successfully:
    res.status(200).json({ error: 'Method Not Allowed' })
    return
  }

  if (req.method === 'POST') {
    try {
      const {
        target_language,
        source_language,
        text_array,
      }: TranslationRequestBody = req.body
      l({ target_language, source_language, text_array })

      const projectId = serviceAccountKey.project_id
      const location = 'us-central1'
      let specialCharacter = '*$*'

      let modifiedArray = text_array.map((str) => {
        // Replace leading spaces, trailing spaces, or both with the special character using regex
        return str
          .replace(/^\s+/, specialCharacter)
          .replace(/\s+$/, specialCharacter)
      })
      const request = {
        parent: `projects/${projectId}/locations/${location}`,
        contents: modifiedArray,
        mimeType: 'text/plain',
        sourceLanguageCode: source_language,
        targetLanguageCode: target_language,
        glossaryConfig: {
          glossary: glossaryPath[target_language],
          ignoreCase: true,
        },
      }

      const [response] = await translationClient.translateText(request)
      const data: any = response.glossaryTranslations
      const response_data: TranslationResponse[] = data.map(
        (elem: any, i: number) => ({
          text: revertSpecialCharacters(text_array[i], specialCharacter),
          translatedText: revertSpecialCharacters(
            elem.translatedText,
            specialCharacter
          ),
        })
      )

      return res.status(200).json({ message: response_data })
    } catch (error) {
      console.error(error)
      return res.status(500).json({ message: 'something went wrong!' })
    }
  } else {
    // Handle other HTTP methods
    return res.status(405).json({ message: 'Method Not Allowed' })
  }
}

function revertSpecialCharacters(string, specialCharacter) {
  const escapedSpecialCharacter = specialCharacter.replace(
    /[$()*+?.[\]^{|}]/g,
    '\\$&'
  )
  const regexStart = new RegExp(`^${escapedSpecialCharacter}+`)
  const regexEnd = new RegExp(`${escapedSpecialCharacter}+$`)

  return string.replace(regexStart, ' ').replace(regexEnd, ' ')
}

export default handler
