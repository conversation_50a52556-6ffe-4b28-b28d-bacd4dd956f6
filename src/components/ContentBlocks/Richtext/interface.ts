import {TipTapBaseNode} from '@wokaylabs/tiptap-react-render/dist/esm/interfaces'
import {KernelI} from '../../Kernel'

/**
 * List interface enlists all the props (and their types) that List can have.
 */
export interface RichtextI extends KernelI {
  //...enlist other
  data?: TipTapBaseNode
  isDocumentation?: boolean
  // if isDarkMode true then darkTheme class will apply and link will have yellow colour
  isDarkMode?: boolean
}