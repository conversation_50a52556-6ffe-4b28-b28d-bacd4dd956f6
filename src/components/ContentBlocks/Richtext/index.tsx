import { TipTapRender } from '@wokaylabs/tiptap-react-render'
import React from 'react'
import Kernel from '../../Kernel'
import { docHandlers, handlers } from './@core/handlers'
import { RichtextD } from './defaults'
import style from './index.module.scss'
import { RichtextI } from './interface'

export default function Richtext(props: RichtextI): React.ReactElement {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: RichtextI = {
    ...RichtextD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      // dark theme class to be applied logically
      className: `${style.richText} ${props.htmlAttr?.className ?? ''} contenter ${props?.isDarkMode ? `darkTheme ${style.darkTheme}` : ''}`,
    },
  }

  function addAriaHidden(node: any) {
    // Check if the node has attributes
    if (node.attrs) {
      // Add aria-hidden: true to the attributes
      node.attrs['aria-hidden'] = true
    }

    if (node.type === 'text') {
      node['attrs'] = {
        ...(node['attrs'] ?? {}),
        'aria-hidden': true,
      }
    }

    // If the node has child content, apply the function recursively
    if (node.content && Array.isArray(node.content)) {
      node.content.forEach(addAriaHidden)
    }

    return node
  }

  if (Boolean(updatedProps.htmlAttr?.['aria-hidden'])) {
    addAriaHidden(updatedProps.data)
  }

  return updatedProps.data ? (
    <Kernel {...updatedProps} as={'div'}>
      <TipTapRender
        handlers={updatedProps.isDocumentation ? docHandlers : handlers}
        node={updatedProps.data}
      />
    </Kernel>
  ) : (
    <></>
  )
}