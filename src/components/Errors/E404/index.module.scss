// importing global variables
//Default SCSS variables are prepended in next.config.js

.ErrorRoot {
  //
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 40px;

  .errorInfo {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 50%;

    @media (max-width: 733px) {
      width: 100%;
    }

    .subHeading {
      color: $cp2;
      font-weight: 500;
      text-transform: uppercase;
      line-height: normal;
      margin: 0;
    }

    .heading {
      font-family: $fSansBld;
      //font-weight: 700;
      margin: 0 0 30px 0;

      @media (max-width: $smScreenSize) {
        // font-size: $sH1FontSize;
        //line-height: 50px;
      }
    }

    .richText {
      // font-weight: 400;
      font-size: $fs4;

      .pageUrl {
        color: #e2222b;
      }

      .p2 {
        margin-top: 20px;
      }
    }

    .menuList {
      margin-top: 40px;
      margin-bottom: 40px;
    }
  }

  .media {
    height: 700px;
    width: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      object-fit: cover;
      border-radius: 4px;
    }

    @media (max-width: $smScreenSize) {
      display: none;
    }
  }

  @media (max-width: $smScreenSize) {
    min-width: $minWidth;
    width: 100%;
  }
}