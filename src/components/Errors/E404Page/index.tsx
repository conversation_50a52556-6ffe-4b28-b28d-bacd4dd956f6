/**
 * E404Page Component - 404 Error Page Handler
 *
 * Client-side component that renders complete 404 error pages with full navigation
 * context and internationalization support. This component serves as the main
 * container for 404 error pages, handling locale detection, data management,
 * and rendering of all page sections with error boundaries.
 *
 * Key Features:
 * - Automatic locale detection from URL parameters and browser settings
 * - Multi-locale support for internationalized error pages
 * - Complete page structure with header, content, footer, and popup
 * - Error boundary protection for each major page section
 * - Development tools integration (cache flush button)
 * - Responsive design and accessibility compliance
 *
 * Data Flow:
 * 1. Receives pre-fetched data from get404Data() server function
 * 2. Detects user's preferred locale from URL or browser settings
 * 3. Selects appropriate locale-specific data for rendering
 * 4. Renders complete page structure with error boundaries
 *
 * Usage:
 * - Used by Next.js not-found.tsx for handling 404 errors
 * - Automatically invoked when users access non-existent routes
 * - Provides consistent error experience across all supported locales
 */
'use client'
import { useEffect, useState } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import {
  BUISNESS_DOMAINS,
  getLocalStorageItem,
  getQueryParam<PERSON>son,
  is<PERSON>rowser,
} from '../../../globals/utils'
import NavigationFooterRouter from '../../../lib/componentsRouter/NavigationFooterRouter'
import NavigationHeaderRouter from '../../../lib/componentsRouter/NavigationHeaderRouter'
import PageRouter from '../../../lib/componentsRouter/PageRouter'
import FlushPageFloatingButton from '../../../utils/FlushPageFloatingButton'
import Popup from '../../Containers/Popup'
import TheError from '../../Kernel/@core/TheError/TheError'

/**
 * E404Page Component Implementation
 *
 * Main functional component that orchestrates the rendering of 404 error pages
 * with complete navigation context and internationalization support.
 *
 * @param {Object} props - Component props containing pre-fetched data
 * @param {Object} props.navigationData - Navigation header data indexed by locale
 * @param {Object} props.footerData - Navigation footer data indexed by locale
 * @param {Object} props.error404Data - Error page content data indexed by locale
 */
function E404Page(props: any) {
  // State management for different page sections
  // Each state holds locale-specific data for rendering
  const [footerData, setFooterData] = useState()
  const [componentData, setComponentData] = useState()
  const [navigationData, setNavigationData] = useState({})

  /**
   * Default Language Localization Configuration
   *
   * Maps language codes to query parameter structures for URL generation.
   * Used for creating language-specific navigation links and maintaining
   * consistent URL patterns across different locales.
   *
   * Each entry contains a slug property with the query parameter format
   * that should be used when switching to that language.
   */
  let defaultLangLocalization = {
    en: { slug: '?lang=en' },
    fr: { slug: '?lang=fr' },
    de: { slug: '?lang=de' },
    es: { slug: '?lang=es' },
    it: { slug: '?lang=it' },
    nl: { slug: '?lang=nl' },
  }

  /**
   * URL Language Code to Locale Mapping
   *
   * Maps short language codes from URLs to full locale identifiers.
   * This enables clean URLs while maintaining proper internationalization.
   * Used for converting URL parameters like ?lang=en to locale en-CA.
   *
   * Note: Some locales use simplified codes (es, it, nl) while others
   * use full region-specific codes (en-CA, fr-CA, de-DE).
   */
  const urlToLocale = {
    en: 'en-CA',
    fr: 'fr-CA',
    de: 'de-DE',
    es: 'es',
    it: 'it',
    nl: 'nl',
  }

  /**
   * Locale Detection from URL and Browser Settings
   *
   * Sophisticated locale detection that prioritizes user preferences:
   * 1. URL query parameter (?lang=en)
   * 2. URL path segment (/en/page)
   * 3. Browser localStorage preference
   * 4. Default fallback (en-CA)
   *
   * Special handling for Altus domain which may have different locale logic.
   * Ensures users see 404 pages in their preferred language.
   *
   * @returns {string} Detected locale identifier (e.g., 'en-CA', 'fr-CA')
   */
  function detectLocaleFromUrl() {
    const url = window.location.href.split('/')
    const lang = getQueryParamJson().lang

    // Special handling for Altus domain
    // May have different locale detection requirements
    const domain = process.env.NEXT_PUBLIC_DOMAIN || ""
    if (BUISNESS_DOMAINS['altus'] === domain) {
      return urlToLocale[lang] || "en-CA"
    }

    // Primary locale detection logic
    // Check query parameter first, then URL path, then localStorage
    if (urlToLocale[lang]) return urlToLocale[lang]
    else if (Object.keys(defaultLangLocalization).includes(url?.[3]))
      return urlToLocale[url?.[3]]
    else return getLocalStorageItem('locale') || 'en-CA'
  }

  // Determine the active locale for this page render
  // Use browser-based detection on client, fallback to default on server
  const locale = isBrowser() ? detectLocaleFromUrl() : 'en-CA'

  /**
   * Component Data Initialization
   *
   * Sets up component state with locale-specific data on mount.
   * Extracts the appropriate data for the detected locale from the
   * pre-fetched multi-locale data structure.
   *
   * This ensures the 404 page renders with content in the user's
   * preferred language and maintains consistent navigation context.
   *
   * Data Flow:
   * - navigationData: Header navigation for the detected locale
   * - footerData: Footer navigation for the detected locale
   * - componentData: Error page content wrapped in array for PageRouter
   */
  useEffect(() => {
    setNavigationData(props?.navigationData?.[locale])
    setFooterData(props?.footerData?.[locale])
    setComponentData([props?.error404Data?.[locale]])
  }, [])

  /**
   * 404 Page Render Structure
   *
   * Renders a complete 404 error page with all necessary components
   * wrapped in error boundaries for maximum reliability. Each major
   * section is isolated to prevent cascading failures.
   *
   * Page Structure:
   * 1. Development tools (cache flush button) - only in non-production
   * 2. Navigation header with locale and language switching
   * 3. Main error page content via PageRouter
   * 4. Navigation footer with locale support
   * 5. Global popup system for notifications
   *
   * Error Boundary Strategy:
   * Each major component is wrapped in its own ErrorBoundary to ensure
   * that if one section fails, the rest of the page remains functional.
   * This is critical for 404 pages as they are often the last resort
   * for users encountering problems.
   */
  return (
    <>
      {/* Development Tools - Cache Management */}
      {/* Only shown in non-production environments for debugging */}
      {process.env.VERCEL_ENV !== 'production' && <FlushPageFloatingButton />}

      {/* Navigation Header Section */}
      {/* Provides primary navigation and language switching capabilities */}
      <ErrorBoundary FallbackComponent={TheError}>
        <NavigationHeaderRouter
          {...navigationData}
          locale={locale}
          langLocalization={defaultLangLocalization}
        />
      </ErrorBoundary>

      {/* Main Error Page Content */}
      {/* Renders the 404 error message, helpful links, and search functionality */}
      <ErrorBoundary FallbackComponent={TheError}>
        <PageRouter
          componentData={componentData}
          pageData={{ template: 'Generic' }}
        />
      </ErrorBoundary>

      {/* Navigation Footer Section */}
      {/* Provides additional navigation options and important links */}
      <ErrorBoundary FallbackComponent={TheError}>
        <NavigationFooterRouter locale={locale} {...footerData} />
      </ErrorBoundary>

      {/* Global Popup System */}
      {/* Handles notifications, modals, and other overlay content */}
      <ErrorBoundary FallbackComponent={TheError}>
        <Popup isMaxedOut={false} />
      </ErrorBoundary>
    </>
  )
}

export default E404Page
