interface TimelineMilestoneItemsProps {
  __typename: string
  sys: Sys
  title?: string
  internalName?: string
  timelineItemsCollection?: TimelineItemsCollection
  locale?: string
  fullUrl?: string
  htmlAttr?: React.ComponentProps<'div'>['htmlAttr']
}

interface Sys {
  id: string
}

interface TimelineItemsCollection {
  items: TimelineItem[]
}

interface TimelineItem {
  __typename: string
  description?: TipTapBaseNode
  startDate?: string
  endDate?: string
  location?: Location
  heading?: string
  subHeading?: string
  milestoneImage?: MilestoneImage
}

interface Location {
  lat: number
  lon: number
}

interface MilestoneImage {
  url: string
  title: string
}
