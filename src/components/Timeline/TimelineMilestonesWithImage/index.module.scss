@keyframes scaleOnce {
  0% {
    transform: scale(0.6);
  }

  // 30% {
  //   transform: scale(1.5);
  // }
  100% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }

  100% {
    transform: scale(3.5);
    opacity: 0;
  }
}

@keyframes pulse1 {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }

  100% {
    transform: scale(4.5);
    opacity: 0;
  }
}

@keyframes progress1 {
  0% {
    transform: scaleY(0);
    opacity: 0.5;
  }

  90% {
    transform: scaleY(1);
    opacity: 1;
  }

  100% {
    transform: scaleY(1);
    opacity: 0;
  }
}

@keyframes progress2 {
  0% {
    transform: translateY(0) scale(0.8);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: translateY(100%) scale(1);
    opacity: 0;
  }
}

@keyframes singlePulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }

  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes singlePulseGrey {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }

  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.scaleOnce {
  animation: scaleOnce 1s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}

.timeMilestonesRoot {
  position: relative;
  min-height: 100vh;
  background: $cp1;
  color: $cs2;
  opacity: 1;

  .timeline {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 2px;
    background-color: #444;
    transform: translateX(-50%);
    z-index: 0;

    @media (max-width: 768px) {
      left: 25px;
      transform: translateX(0);
    }

    .timelineProgressWrapper {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: visible;

      .timelineProgress {
        position: absolute;
        top: 0;
        left: -1px;
        width: 4px;
        height: 0%;
        background: linear-gradient(to bottom, $cs3, $cs1);
        z-index: 1;
        border-bottom-left-radius: 50pt;
        border-bottom-right-radius: 50pt;

        .progressDot {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          width: 5px;
          height: 5px;
          background-color: $cs1;
          border-radius: 50%;
          z-index: 2;
          bottom: 0;
        }
      }
    }
  }

  .timeMilestonesInnerRoot {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 100px;
    padding: 100px 0;
    position: relative;

    @media (max-width: $smScreenSize) {
      gap: 60px;
    }

    .timelineMilestone {
      display: flex;
      align-items: center;
      gap: 100px;
      width: 100%;
      padding: 0 20px;
      position: relative;

      @media (max-width: $mScreenSize) {
        gap: 70px;
      }

      @media (max-width: $smScreenSize) {
        gap: 50px;
      }

      &.inActive {
        opacity: 0.25;

        .dot {
          position: relative;
          display: inline-block;
          width: 16px;
          height: 16px;
          background-color: $cp1;
          border-radius: 50%;
          opacity: 1;
          z-index: 1;
          transform: scale(0.4);

          @media (max-width: 767px) {
            left: -35px;

            &:before {
              left: 6px !important;
            }

            &:after {
              left: 6px !important;
            }
          }

          &::before {
            content: '';
            position: absolute;
            top: -13px;
            // left: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(240, 240, 240, 0.4);
            z-index: -1;
          }

          &::after {
            content: '';
            position: absolute;
            top: -5px;
            // left: -4px;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 24px;
            background-color: #000b3d;
            border: 2px solid #666;
            border-radius: 50%;
            z-index: 1;
          }

          &.pulsate {
            animation: none;
          }
        }

        .card,
        .descriptionRoot {
          filter: grayscale(1);
          opacity: 0.25 !important;
          transition: all 1s ease;
        }
      }

      &.active {
        .card {
          aspect-ratio: 16/9;
          width: 100%;
          border-radius: 5px;
          opacity: 1 !important;
          box-shadow:
            rgba(0, 0, 0, 0.4) 0px 50px 100px -20px,
            rgba(0, 0, 0, 0.5) 0px 30px 60px -30px;
        }
      }
      @media (min-width: 768px) {
        flex-direction: row;
        justify-content: center;
        &.milestoneReverselayout:nth-child(even) {
          flex-direction: row-reverse;
        }
      }

      @media (max-width: 767px) {
        flex-direction: column;
        align-items: flex-start;
        padding-left: 54px;
        gap: 10px;
      }

      .dot {
        position: relative;
        transition: all 0.4s ease;
        left: 0;
        width: 16px;
        height: 16px;
        background-color: #c9f344;
        border-radius: 50%;
        z-index: 2;
        // transform: scale(.55);
        will-change: transform;

        @media (max-width: 767px) {
          left: -36px;
        }

        &.filled {
          transform: scale(1);

          &::before,
          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            z-index: -1;
            background-color: #c9f344;
            // transform: scale(1.2);
            opacity: 0.7;
          }

          &::before {
            animation: pulse1 3s ease-in 0s infinite;
          }

          &::after {
            animation: pulse 3s ease-in 1s infinite;
          }
        }

        .hoverPulseRing {
          pointer-events: none;
          position: absolute;
          top: -13px;
          left: -12px;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          z-index: -3;
          background-color: rgba(255, 255, 255, 0.4);
          opacity: 0;
          transform: scale(1);
          transition: none;
        }

        &.dotGrey {
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: -12px;
            left: -12px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.4);
            z-index: -1;
            animation: singlePulseGrey 2s ease-in 1 forwards;
          }
        }
      }

      .card,
      .descriptionRoot {
        flex: 1;
        min-width: 296px;
        max-width: 500px;
        transition: all 1s ease;

        &.inActive {
          filter: grayscale(1);
          opacity: 0.25;
        }
      }

      .card {
        position: relative;
        aspect-ratio: 16 / 9;
        width: 100%;
        border-radius: 5px;
        max-height: 350px;
        overflow: hidden;

        @media (max-width: $mScreenSize) {
          max-height: 250px;
        }

        .cardImage {
          position: absolute;
          inset: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 5px;
        }

        &.active {
          box-shadow:
            rgba(0, 0, 0, 0.4) 0px 50px 100px -20px,
            rgba(0, 0, 0, 0.5) 0px 30px 60px -30px;
        }
      }

      .descriptionRoot {
        .timelineYear {
          color: $cs1;
          text-box: trim-both cap alphabetic;
          margin-bottom: 24px;
        }

        .timelineHeading {
          line-height: 1.2;
          transition: all 0.4s ease;
          font-family: $fSansBld;
          background: linear-gradient(90deg, $cs1, $cs3);
          background-clip: text;
          color: transparent;
        }

        .timelineContent {
          font-size: 1.12rem;
          text-box: trim-both cap alphabetic;
          line-height: 1.5;
          margin-top: 16px;
        }
      }

      &:not(.active):hover {
        cursor: pointer;

        .hoverPulseRing {
          animation: singlePulse 1.5s ease-in 1 forwards;
        }

        .card,
        .descriptionRoot {
          opacity: 0.75 !important;
        }
      }
    }
  }
}

.timelineMilestone:has(.timelineContent) .timelineHeading {
  font-size: 1.75rem;
}

.timelineMilestone:not(:has(.timelineContent)) .timelineHeading {
  font-size: 2.25rem;
}

.hoverOnce {
  animation: singlePulse 1.5s ease-in 1 forwards;
}
.hidden {
  opacity: 0;
  transition: opacity 2s ease-in;
  pointer-events: none; // optional
}

.show {
  opacity: 1;
  transition: opacity 2s ease-in;
}
