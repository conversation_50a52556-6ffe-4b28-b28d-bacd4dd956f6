interface TimelineMilestonesWithTextProps {
  __typename: string
  sys: Sys
  title?: string
  internalName?: string
  timelineItemsCollection?: TimelineMilestonesWithTextCollection
  locale?: string
  fullUrl?: string
  htmlAttr?: React.ComponentProps<'div'>['htmlAttr']
}

interface Sys {
  id: string
}

interface TimelineMilestonesWithTextCollection {
  items: TimelineMilestonesWithTextItem[]
}

interface TimelineMilestonesWithTextItem {
  __typename: string
  description?: TipTapBaseNode
  startDate?: string
  endDate?: string
  location?: Location
  heading?: string
  subHeading?: string
  cardTextContent?: TipTapBaseNode
  // milestoneImage?: MilestoneImage
}

interface Location {
  lat: number
  lon: number
}

// interface MilestoneImage {
//   url: string
//   title: string
// }
