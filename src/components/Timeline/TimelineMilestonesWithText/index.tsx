'use client'
import { motion, useAnimation } from 'framer-motion'
import { useEffect, useRef } from 'react'
import { TheSEOBlock } from '../../../globals/utils'
import Richtext from '../../ContentBlocks/Richtext'
import styles from './index.module.scss'

const TimelineMilestonesWithText = (props: TimelineMilestonesWithTextProps) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const progressRef = useRef<HTMLDivElement>(null)
  const dotRefs = useRef<HTMLDivElement[]>([])
  const controls = useRef<ReturnType<typeof useAnimation>[]>([])

  const updatedProps = {
    ...props,
    className: `${props?.htmlAttr?.className || ''}`,
  }

  controls.current = []

  const handleScroll = () => {
    if (!containerRef.current) return

    const rect = containerRef.current.getBoundingClientRect()
    const windowHeight = window.innerHeight
    const centerY = windowHeight * 0.5005
    const height = windowHeight

    const totalHeight = rect.height
    const scrolledY = Math.min(totalHeight, Math.max(0, centerY - rect.top))
    const progress = (scrolledY / totalHeight) * 100

    if (progressRef.current) {
      progressRef.current.style.height = `${progress}%`
    }
    let lastFilledIndex = -1

    dotRefs.current.forEach((dot, index) => {
      const dotRect = dot.getBoundingClientRect()
      const dotCenterY = dotRect.top + dotRect.height / 2

      if (dotCenterY <= centerY) {
        lastFilledIndex = index
      }
      if (dotCenterY <= height) {
        // Animate the dot to be filled
        controls.current[index]?.start({
          opacity: 1,
          y: 0,
          transition: { duration: 1 },
        })
      }
    })

    dotRefs.current.forEach((dot, index) => {
      if (index === lastFilledIndex) {
        // for active dot
        const parent = dot.parentElement
        dot.classList.add(styles.scaleOnce)
        dot.classList.add(styles.dotGrey)
        dot.classList.add(styles.filled)
        dot.classList.remove(styles.dotGrey)

        parent?.classList.add(styles.active)
        parent?.classList.remove(styles.inActive)
      } else {
        // for inactive dots
        dot.classList.remove(
          styles.pulsate,
          styles.filled,
          styles.scaleOnce,
          styles.pulsate3x,
          styles.pulsateHover,
          styles.dotGrey
        )
        dot.parentElement?.classList.add(styles.inActive)
        dot.parentElement?.classList.remove(styles.active)
      }
    })
  }

  useEffect(() => {
    window.addEventListener('scroll', handleScroll)
    window.addEventListener('resize', handleScroll)
    handleScroll()
    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleScroll)
    }
  }, [])
  // Clear refs before rendering
  dotRefs.current = []
  const generateTimelineJsonLd = (title: string, items: TimelineItem[]) => {
    if (!title || !items || items.length === 0) return null

    const itemListElement = items
      .map((item, index) => {
        if (
          !item?.heading || // Required: Name
          !item?.subHeading || // Required: Description
          !item?.startDate || // Required: Start Date
          !item?.location // Required: Location
        ) {
          return null
        }

        return {
          '@type': 'Event',
          position: index + 1,
          name: item.heading,
          description: item.subHeading,
          startDate: item.startDate,
          ...(item.endDate && { endDate: item.endDate }),
          location: {
            '@type': 'Place',
            name: item.location,
          },
          ...(item?.milestoneImage?.url && { image: item.milestoneImage.url }),
        }
      })
      .filter(Boolean)

    if (itemListElement.length === 0) return null

    return {
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      name: title,
      itemListElement,
    }
  }

  const seoSchema = generateTimelineJsonLd(
    props?.title ?? '',
    props?.timelineItemsCollection?.items ?? []
  )
  return (
    <>
      <div
        style={updatedProps?.htmlAttr?.style}
        className={`${styles.timeMilestonesRoot} bleedf ${updatedProps.className}`}
        ref={containerRef}
      >
        {seoSchema && <TheSEOBlock seoObj={seoSchema} />}
        <div className={`${styles.timeline}`}>
          <div className={styles.timelineProgressWrapper}>
            <div className={styles.timelineProgress} ref={progressRef}>
              <span className={styles.progressDot} id='progress-dot'></span>
            </div>
          </div>
        </div>

        <div className={styles.timeMilestonesInnerRoot}>
          {props?.timelineItemsCollection?.items?.map((Singletimeline, i) => {
            const control = useAnimation()
            controls.current[i] = control
            return (
              <motion.div
                key={i}
                // reverse layout in httmlattr for alternate layout
                className={`${styles.timelineMilestone} ${updatedProps?.htmlAttr?.reverse === 'true' ? styles.milestoneReverselayout : ''}`}
                initial={{ opacity: 0, y: 40 }}
                transition={{ duration: 0.6 }}
                animate={control}
                onClick={() => {
                  const el = dotRefs.current[i]
                  el?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                  })

                  handleScroll()
                }}
                onMouseEnter={() => {
                  const dotEl = dotRefs.current[i]
                  const parent = dotEl?.parentElement

                  if (
                    parent &&
                    !parent.classList.contains(styles.hoverOnce) &&
                    parent.classList.contains(styles.inActive)
                  ) {
                    // Find the span inside the dot
                    const spanEl = dotEl.querySelector(
                      `.${styles.hoverPulseRing}`
                    )
                    if (
                      spanEl &&
                      !spanEl.classList.contains(styles.hoverOnce)
                    ) {
                      spanEl.classList.add(styles.hoverOnce)

                      setTimeout(() => {
                        spanEl.classList.remove(styles.hoverOnce)
                      }, 1500) // match animation length
                    }
                  }
                }}
              >
                {/* Animated Card */}
                <motion.div
                  className={`${styles.descriptionRoot} ${styles.leftDescription}`}
                  initial={{ x: 0, opacity: 0 }}
                  animate={{
                    x: 0,
                    opacity: 1,
                  }}
                  transition={{ type: 'spring', stiffness: 40, duration: 0.6 }}
                >
                  {Singletimeline?.cardTextContent && (
                    <Richtext data={Singletimeline?.cardTextContent} />
                  )}
                </motion.div>

                {/* Animated Dot */}
                <motion.div
                  className={`${styles.dot}`}
                  ref={(el) => {
                    if (el) {
                      dotRefs.current[i] = el
                    }
                  }}
                  transition={{ duration: 0.5 }}
                >
                  <span className={styles.hoverPulseRing}></span>
                </motion.div>

                {/* Description with Slide */}
                <motion.div
                  className={`${styles.descriptionRoot} `}
                  initial={{ x: 0, opacity: 0 }}
                  animate={{
                    x: 0,
                    opacity: 1,
                  }}
                  transition={{ type: 'spring', stiffness: 40, duration: 0.6 }}
                >
                  {Singletimeline?.heading && (
                    <h1 className={`${styles.timelineYear} `}>
                      {Singletimeline?.heading}
                    </h1>
                  )}
                  <p className={styles.timelineHeading}>
                    {Singletimeline?.subHeading}
                  </p>

                  {Singletimeline?.description && (
                    <Richtext
                      htmlAttr={{ className: styles.timelineContent }}
                      data={Singletimeline?.description}
                    />
                  )}
                </motion.div>
              </motion.div>
            )
          })}
        </div>
      </div>
    </>
  )
}

export default TimelineMilestonesWithText
