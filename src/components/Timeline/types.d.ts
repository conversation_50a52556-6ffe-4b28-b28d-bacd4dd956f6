// Description: Type definitions for Timeline components
// TimelineMilestoneItemsProps and TimelineMilestonesWithTextProps

// how to extend an interface in TypeScript
import { TimelineMilestoneItemsProps } from './TimelineMilestoneItemsProps'
import { TimelineMilestonesWithTextProps } from './TimelineMilestonesWithTextProps'
interface TimelineProps
  extends TimelineMilestoneItemsProps,
    TimelineMilestonesWithTextProps {
  template?: string
}
