import { Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import { TooltipText } from '../../../globals/defaults'
import {
  FetchNotifications,
  setIsNotificationCenterOpen,
} from '../../../redux/slices/notificationCenterSlice'
import { useAppDispatch, useAppSelector } from '../../../redux/store'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import styles from './index.module.scss'

type NotificationBellProps = {
  updatedProps?: {
    isLightMode?: boolean
    isTranslucent?: boolean
  }
  customClassName?: string
  wrapperTag?: 'div' | 'ul'
  extraClass?: string
  isHoveredOver?: boolean
  iconColor: string
  enableHoverEffect?: boolean
}

const NotificationBell: React.FC<NotificationBellProps> = ({
  updatedProps,
  customClassName = '',
  wrapperTag = 'div',
  extraClass = '',
  isHoveredOver = false,
  iconColor,
  enableHoverEffect = true,
}) => {
  let [isNewNotification, setIsNewNotification] = useState(false)

  const { notificationPermission, isNotificationCenterOpen, isSafari } =
    useAppSelector((state) => state.notiCenter)

  const isNewNotificationReceived = useAppSelector(
    (state) => state.notiCenter.isNewNotification
  )

  const isNotificationsAllowed = notificationPermission === 'granted'

  const dispatch = useAppDispatch()

  let tooltipTitle = isSafari
    ? isNewNotification
      ? TooltipText.newNotification
      : TooltipText.openNotificationCenter
    : !isNotificationsAllowed
      ? TooltipText.clickToAllowNotifications
      : isNewNotification
        ? TooltipText.newNotification
        : TooltipText.openNotificationCenter

  const Wrapper = wrapperTag

  useEffect(() => {
    setIsNewNotification(isNewNotificationReceived)
  }, [isNewNotificationReceived])

  useEffect(() => {
    dispatch(FetchNotifications(process.env.NEXT_PUBLIC_DOMAIN))
  }, [isNotificationCenterOpen])

  /**
   * HandleBellIconClick function is used to open the Notification Center by setting its visibility to true in the store.
   */
  const handleBellIconClick = async () => {
    dispatch(setIsNotificationCenterOpen(true))
  }

  return (
    <Wrapper
      onClick={handleBellIconClick}
      className={`${customClassName} ${isHoveredOver || !updatedProps?.isTranslucent ? `${styles.lightMode} ${styles.bellIconRoot}` : styles.bellIconRoot}`}
    >
      <li className={styles.bellIconLi}>
        <Tooltip title={tooltipTitle} arrow>
          <div
            className={`${(isNewNotification || (!isSafari && !isNotificationsAllowed)) && styles.animatedBorderWrapper} ${enableHoverEffect ? styles.hoverEnabled : ''} ${extraClass} `}
          >
            <GenericIcon
              icon='Bell'
              size='sm'
              isRounded
              htmlAttr={{
                className: `${styles.animatedGradient} ${iconColor}`,
              }}
            />
          </div>
        </Tooltip>
      </li>
    </Wrapper>
  )
}

export default NotificationBell
