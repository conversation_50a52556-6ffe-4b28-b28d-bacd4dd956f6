
.notificationCenterRoot {
  position: fixed;
  top: 0;
  right: 0;
  min-width: 440px;
  height: 100vh;
  z-index: 9;

  @media (max-width: $smScreenSize) {
    max-width: 380px;
    min-width: 390px;
    width: 100%;
    }

  hr {
    width: 100%;
    margin: 20px 0;
    opacity: .15;
    }

  .notificationCenter {
    position: absolute;
    right: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-left: 3px;
    top: 0;
    backdrop-filter: blur(2rem);
    background: rgba(255, 255, 255, .3);
    border-left: .05rem solid rgba(255, 255, 255, 0.5);
    box-shadow: rgba(50, 50, 93, 0.25) 0px 50px 100px -20px, rgba(0, 0, 0, 0.3) 0px 30px 60px -30px;

    .content {
      width: 100%;
      height: 100%;

      .tabsRoot {
        position: relative;
        display: flex;
        width: 100%;
        background: rgba(176, 216, 229, 0.075);
        backdrop-filter: blur(2rem);
        box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;

        .backdrop {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          backdrop-filter: blur(2rem);
          z-index: 0;
          }

        .tab1, .tab2 {
          flex: 1;
          border: 1px solid rgba(255, 255, 255, 0.75);
          background: rgba(255, 255, 255, 0.25);
          display: flex;
          align-items: center;
          justify-content: center;
          height: 40px;
          z-index: 1;
          position: relative;
          cursor: pointer;
          }

        .tab3 {
          width: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1;
          position: relative;
          cursor: pointer;
          }

        .tab3 {
          &:hover {
            svg {
              color: $cs2 !important;
              transition: all 0.25s ease;
              }
            }
          }

        .tab1, .tab2, .tab3 {

          p {
            font-size: 0.8rem;
            }

          &:hover {
            background-color: $cp1;
            color: $cs2;
            transition: all 0.25s ease;
            }
          }
        }


      .headerRoot {
        position: sticky;
        // display: flex;
        // flex-direction: column;
        // align-items: center;
        // justify-content: space-between;
        top: 0;
        z-index: 10;
        overflow: hidden;
        padding: 20px;
        border-bottom: 0.05em solid rgba(255, 255, 255, 1);
        // box-shadow: rgba(17, 17, 26, 0.1) 0px 0px 16px;

        @media (max-width: $mScreenSize) {
          padding: 15px;
          }

        .iconsGroup {
          display: flex;
          // justify-content: center;
          align-items: center;
          margin-top: -15px;
          z-index: 1;

          .settingsIcon {
            border-radius: 5px;
            }
          }

        .blurLayer {
          position: absolute;
          inset: 0;
          // background: rgba(255, 255, 255, 0.15);
          backdrop-filter: blur(2.5rem);
          -webkit-backdrop-filter: blur(2.5rem);
          z-index: 0;

          /* Optional: smooth bottom fade */
          mask-image: linear-gradient(to bottom, black 100%, transparent 100%);
          -webkit-mask-image: linear-gradient(to bottom, black 100%, transparent 100%);
          }

        .headerContentRoot {
          display: flex;
          justify-content: space-between;
          // align-items: center;
          min-width: 100%;
          }

        .headerContent {
          position: relative;
          z-index: 1;
          color: $cn1;
          text-box: trim-both cap alphabetic;
          }

        .headerContentTitle {
          // margin-bottom: 16px;
          font-size: 1rem;
          letter-spacing: 0.04em;
          text-transform: uppercase;
          text-shadow: 0 1px 0 rgba(255, 255, 255, 1);
          text-box: trim-both cap alphabetic;
          font-family: $fSansBld;
          margin-top: 4px;
          }

        .headerContentDescription {
          letter-spacing: 0.01em;
          color: $cn2;
          position: relative;
          font-size: 14px;
          z-index: 1;
          text-shadow: 0 1px 0 rgba(255, 255, 255, 1);
          text-box: trim-both cap alphabetic;
          margin-top: 10px;
          }


        // margin-bottom: 16px;
        //   text-shadow: 0 1px 0 rgba(255, 255, 255, 1);
        //   color: $cp2;

        .checkIcon, .closeIcon {
          z-index: 1;
          width: 45px;
          height: 45px;
          // margin-top: -30px;
          }

        .checkIcon {
          &:hover {
            svg {
              color: $ca3 !important;
              transition: all 0.25s ease;
              }
            }
          }

        .closeIcon {
          &:hover {
            svg {
              color: $ca10 !important;
              transition: all 0.25s ease;
              }
            }
          }
        }

      .group {
        // padding: 16px 20px 32px 20px;
        padding: 0;
        max-height: calc(100vh - 130px);
        // overflow-y: auto;
        overflow-x: hidden;
        box-sizing: border-box;
        height: 100vh;
        display: flex;
        flex-direction: column;
        gap: 20px;

        /* Hide scrollbar for IE, Edge and Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */

        .scroll-container::-webkit-scrollbar {
          display: none;
          }
        }

      }

    .notificationCenterBg {
      width: 100%;
      height: 100%;
      position: absolute;
      z-index: -1;
      // background-image: radial-gradient(circle at center bottom, #0082ab, #c9f344, rgb(12, 74, 110));
      background-image: radial-gradient(circle at center bottom, #0028d7, #fff, rgb(176, 188, 243));
      // background-blend-mode: screen, overlay, hard-light, color-burn, color-dodge, normal;
      opacity: .15;
      }

    .notificationCenterBgtop {
      width: 100%;
      height: 50%;
      position: absolute;
      z-index: -1;
      padding: 20px;
      // background-image: conic-gradient(from 80deg at -3% 0%, rgba(255, 255, 255, 1) 0%, rgba(14, 165, 233, 0.5) 50%, transparent 100%);
      background-image: conic-gradient(from 80deg at -3% 0%, rgb(255, 255, 255) 0%, rgba(176, 188, 243, 0.5) 50%, transparent 100%);
      mask-image: linear-gradient(to bottom, black 60%, transparent 100%);
      -webkit-mask-image: linear-gradient(to bottom, black 60%, transparent 100%);
      opacity: 1;
      }

    .dropdownRoot {
      position: relative;
      }

    .dropdownDiv::-ms-expand {
      display: none;
      }

    .select {
      position: relative;
      }

    .select svg {
      content: url("../../multimedia/ChevronDown.svg");
      right: 15px;
      font-size: 20px;
      width: 15px;
      height: 15px;
      top: 50%;
      transform: translateY(-50%);
      position: absolute;
      pointer-events: none;
      color: $cs1;
      }

    .formControl {
      display: block;
      width: 100%;
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.5;
      // color: #212529;
      // background-color: #fff;
      background-clip: padding-box;
      border: 1px solid #ced4da;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      border-radius: 0.25rem;
      transition: border-color 0.15s ease-in-out,
      box-shadow 0.15s ease-in-out;
      }

    .formControl:focus {
      // color: #212529;
      // background-color: #fff;
      border-color: #86b7fe;
      outline: 0;
      box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
      }
    }
  }

.notificationEmpty {
  position: relative;
  width: 100%;
  overflow: hidden;
  word-wrap: break-word;
  border: 1px solid rgba(255, 255, 255, 0.5);
  background: radial-gradient(at center top, rgb(255, 255, 255), rgba(255, 255, 255, 0.5));
  z-index: 1;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 8px;

  .notificationEmptyText {
    font-family: "Aeonik Regular", sans-serif;
    text-box: trim-both cap alphabetic;
    font-size: 1rem;
    line-height: 1.5;
    color: #757575;
    }
  }

.bgGradient {
  // position: absolute;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 30px 60px -12px, rgba(0, 0, 0, 0.3) 0px 18px 36px -18px;
  // top: 64px;
  // right: 20px;
  transform: none;
  width: 100%;
  height: calc(100vh - 130px);
  // overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  opacity: .85;
  // background-color: rgba(0, 0, 0, 0.75);
  background-color: #333c64;
  background-image: linear-gradient(315deg, rgba(51, 60, 100, 0.75) 0%, rgba(84, 102, 29, 0.5) 94%);

  // border-radius: 15px;
  // z-index: 15;
  display: flex;
  flex-direction: column;
  // padding: 24px;
  // border: 3px solid white;

  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */

  .scroll-container::-webkit-scrollbar {
    display: none;
    }

  }

.noNotificationWrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
  // justify-content: center;
  align-items: center;

  .noNotificationHeading {
    font-size: 1rem;
    font-family: $fSansBld;
    color: white;
    }


  }

.settingsTooltipArrow {
  position: absolute;
  top: -10px;
  right: 100px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid rgba(51, 60, 100, 0.85);
  z-index: 16;
  }

.settingsLabel {
  font-size: 1.124rem;
  line-height: 1.3;
  // margin-bottom: 4px;
  display: block;
  color: #ccc;
  margin-bottom: 16px;
  }

.settingsContent {
  flex: 1;
  // overflow-y: auto;
  display: flex;
  flex-direction: column;
  // gap: 16px;
  color: white;


  .settingsInnerHeading {
    font-size: 14px;
    font-family: $fSansBld;

    label {
      padding-right: 15px;
      }
    }

  select {
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 4px;
    width: 100%;
    margin-bottom: 8px;
    }

  input[type="checkbox"] {
    margin-right: 8px;
    }

  .dropdownSection {
    display: flex;
    flex-direction: column;
    }

  .checkboxSection {
    display: flex;
    flex-direction: column;
    }

  .checkboxRoot {
    // margin-left: -15px;
    }


  .checkboxColumns {
    // display: flex;
    // gap: 24px;
    // margin-top: 8px;
    display: grid;
    grid-template-columns: 1fr 2fr;
    // row-gap: 12px;
    // margin-top: 8px;
    // column-gap: 10px;
    }

  .checkboxColumn {
    display: flex;
    flex-direction: column;
    gap: 8px;
    // flex: 1 0 45%;

    label {
      min-width: 100%;
      padding-right: 10px;
      transition: all .5s ease-out;
      }
    }
  }

.settingsBackdrop {
  position: absolute;
  top: 80px;
  left: 3px;
  width: calc(100% - 6px);
  height: calc(100% - 80px);
  z-index: 5;
  pointer-events: none;

  background: rgba(255, 255, 255, 0.8);
  }

.settingsHeadingRow {
  // display: flex;
  // align-items: center;
  // justify-content: space-between;
  // margin-bottom: 20px;

  .personaliseHeading {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    }

  .settingsHeading {
    font-size: 1.24rem;
    font-family: $fSansBld;
    // color: white;
    }

  .savedStatus {
    font-size: 14px !important;
    height: auto !important;
    padding: 4px 10px !important;
    }
  }

.halfOpacity {
  opacity: 0.5;
  // cursor: not-allowed;

  // span:first-child{
  //   cursor: not-allowed !important;
  //   }

  &:hover {
    background-color: transparent !important;

    span:nth-child(2) {
      color: white;
      }
    }
  }

.noInteraction {
  pointer-events: none;
  overflow: hidden;
  }

.subheading {
  text-transform: uppercase;
  font-size: 14px;
  font-family: $fSansBld;

  }

.headingWIcon {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-bottom: 8px;
  }

.headingIcon {
  margin-left: -7px;
  cursor: default;
  }

.iconWrap {
  position: relative;
  width: 32px; // or whatever fits both icons well
  height: 32px;
  display: inline-block;
  }

.iconInner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  }

.personaliseIcon {
  margin-left: -11px;
  margin-top: -7px;
  cursor: default;
  }

.tickIcon {
  margin-left: -7px;
  }

.tabIcon {
  border: 1px solid $cs2;
  // border-radius: 5px;
  width: 32px !important;
  height: 32px !important;
  }

.dNone {
  display: none;
  }

.blackOverlay {
  position: absolute;
  top: 152px;
  width: 100%;
  height: 100vh;
  left: 0;
  backdrop-filter: blur(2rem);
  z-index: 2;
  opacity: .85;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
  background: linear-gradient(0deg, black, rgba(0, 0, 0, .5));
  overflow: hidden;

  @media (max-width: $smScreenSize) {
    top: 140px;
    }
  }

.invisible {
  height: 30px;
  }

.enhancedButton {
  border-radius: 5px !important;
  }

.pulsateTab {
  background-color: #fafeec;
  border: 1px solid #b7dd3e;

  &:hover {
    background-color: #fafeec;
    }

  &:active {
    color: $cp2 !important;
    }

  span:nth-child(2) {
    margin-top: 0 !important;
    }
  }

@keyframes spin {
  0% {
    transform: rotate(0deg);
    }
  100% {
    transform: rotate(360deg);
    }
  }

@keyframes pulseShadow {
  0% {
    box-shadow: 0 0 0 0 rgba($cs3, 1);
    }
  70% {
    box-shadow: 0 0 0 5px rgba($cs3, 0.5);
    }
  100% {
    box-shadow: 0 0 0 5px rgba($cs3, 0.3);
    }
  }

.animatedBorderWrapper {
  position: relative;
  display: inline-block;
  padding: 4px;
  border-radius: 50%;
  background: none;
  animation: pulseShadow 2s ease-out infinite;
  z-index: 3;

  .animatedBell {
    width: 24px !important;
    height: 24px !important;
    }

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: conic-gradient($cs3, $cs1, $cs3);
    animation: spin 2s linear infinite;
    z-index: 0;
    }

  &::after {
    content: "";
    position: absolute;
    inset: 3px;
    background-color: $cn8;
    border-radius: 50%;
    z-index: 1;
    transition: background-color 0.3s ease;
    }

  &:hover::after {
    background-color: $cs2 !important;
    }


  > * {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    }
  }