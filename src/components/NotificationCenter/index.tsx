'use client'

import { Tooltip } from 'antd'
import { AnimatePresence, motion } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'
import { useDispatch } from 'react-redux'
import { TooltipText } from '../../globals/defaults'
import { getLocalStorageItem, setLocalStorageItem } from '../../globals/utils'
import {
  FetchNotifications,
  savePreferences,
  setIsNewNotification,
  setIsNotificationCenterOpen,
  setLatestNotifications,
  setNotificationPermission,
  setPreviousNotifications,
} from '../../redux/slices/notificationCenterSlice'
import { useAppSelector } from '../../redux/store'
import Confetti from '../Confetti'
import GenericIcon from '../Multimedia/Icons/SysIcon'
import { saveNotificationsToStorage } from '../PushNotifications/helpers'
import FramerTabsController from '../Tabs/FramerTab'
import FramerTabItem from '../Tabs/FramerTab/@core/FramerTabItem'
import CustomNotification from './CustomNotification'
import styles from './index.module.scss'
import NoNotificationDiv from './NoNotificationCard'
import NotificationPreferences from './NotificationsPrefrences/NotificationPreferences'
import {
  AllowedNotificationType,
  HandleBrowserNotificationPermission,
  HandleSafariNotificationPermission,
  syncToIndexedDB,
} from './utils'

const PREF_SAVE_INTERVAL = 1000

/**
 * The NotificationCenter component provides a UI for displaying and managing notifications.
 * It integrates with the Redux store to fetch and update the notification state, and uses
 * local storage to persist user preferences and notification data. The component features
 * tabs for displaying the latest updates, previous notifications, and user preferences.
 * It also handles browser notification permissions, provides animations for notification
 * transitions, and includes a confetti effect for celebratory moments.
 */

const NotificationCenter = () => {
  const {
    isNotificationCenterOpen,
    latestNotifications,
    previousNotifications,
    notificationPermission,
    isSafari,
    isNewNotification,
  } = useAppSelector((state) => state?.notiCenter)

  const notiRef = useRef<HTMLDivElement>(null)

  const dispatch = useDispatch()

  const domain = process.env.NEXT_PUBLIC_DOMAIN

  const [activeTab, setActiveTab] = useState(1)

  const [isPrefSaved, setIsPrefSaved] = useState(false)

  const [preferences, setPreferences] = useState<any>({})

  const [isNotiPermissionLoading, setIsNotiPermissionLoading] = useState(false)

  const isNotificationAllowed = notificationPermission === 'granted'

  const [isSlidingOut, setIsSlidingOut] = useState(false)

  const [showConfetti, setShowConfetti] = useState(false)

  const [isClearAllAnimationCompleted, setIsClearAllAnimationCompleted] =
    useState(false)

  const handleDismiss = () => setIsSlidingOut(true)

  /**
   * Handle request for notification permission.
   * If not allowed, request permission. If Safari, use HandleSafariNotificationPermission.
   * If not Safari, use HandleBrowserNotificationPermission.
   * If permission is granted, set notification permission in redux store.
   * Set isNotiPermissionLoading to true, and false after 2 seconds.
   */
  const handleAllowNotifications = async () => {
    // notification request permission handler func
    if (!isNotificationAllowed) {
      setIsNotiPermissionLoading(true)
      let permission = ''
      if (isSafari) permission = await HandleSafariNotificationPermission()
      else permission = await HandleBrowserNotificationPermission()
      if (permission === 'granted')
        dispatch(setNotificationPermission(permission))

      setTimeout(() => {
        setIsNotiPermissionLoading(false)
      }, 2000)
    }
  }

  useEffect(() => {
    // when user is inside latest updates mark new notification as false
    if (isClearAllAnimationCompleted) {
      handleCloseAllNotification()
      setIsSlidingOut(false)
      setIsClearAllAnimationCompleted(false)
    }
  }, [isClearAllAnimationCompleted])

  // use this when user is on the Preferences tab or History Tab and a new notification comes in
  const isNewUnNoticedNotification = isNewNotification && activeTab !== 1
  // const isNewUnNoticedNotification = true

  useEffect(() => {
    // when user is inside latest updates mark new notification as false
    if (isNewNotification && activeTab === 1 && isNotificationCenterOpen) {
      dispatch(setIsNewNotification(false))
    }
  }, [isNewNotification, activeTab, isNotificationCenterOpen])

  useEffect(() => {
    // close notification center when user clicks outside
    function handleClickOutside(event: MouseEvent) {
      if (
        notiRef?.current &&
        !notiRef?.current?.contains(event?.target as Node)
      ) {
        handleNotiCenterClose() // Close sidebar
      }
    }

    if (isNotificationCenterOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isNotificationCenterOpen])

  /**
   * Check if active tab is latest updates or previous notifications and if there are no notifications.
   * @returns {boolean} True if active tab is latest updates or previous notifications and there are no notifications, false otherwise.
   */
  const isActiveTabNotiZero = () => {
    // check if active tab is latest updates or previous notifications and if there are no notifications
    if (activeTab === 1) if (latestNotifications?.length === 0) return true
    if (activeTab === 2) if (previousNotifications?.length === 0) return true

    return false
  }

  useEffect(() => {
    // fetch notifications from local storage when notification center is open
    if (isNotificationCenterOpen) dispatch(FetchNotifications(domain))

    if (isSafari) setActiveTab(0)
    // set active tab to 0 if notification is not allowed yet
    else if (isNotificationCenterOpen && !isNotificationAllowed) setActiveTab(0)
    else setActiveTab(1)

    // fetch preferences from local storage, if no preferences found then store default preferences
    const prefs = getLocalStorageItem('preferences')
    if (prefs === null) {
      storeDefaultPrefs()
    } else {
      let preferences = {}

      if (typeof prefs === 'string') {
        preferences = JSON.parse(prefs || '{}')
      } else preferences = prefs || {}

      setPreferences(preferences)
      dispatch(savePreferences(preferences))
    }
  }, [isNotificationCenterOpen, isSafari])

  /**
   * Clears a notification from the latest updates and moves it to the previous notifications.
   *
   * @param id - The unique identifier of the notification to be cleared.
   *
   * This function finds the notification from the latest updates using the provided id,
   * removes it from the latest notifications, and then adds it to the previous notifications.
   * It updates the Redux store with the new lists of previous and latest notifications,
   * and saves these updates to local storage.
   */

  const clearNotificationFromLatestUpdate = (id: string) => {
    // handler func to clear notification from latest updates and move to previous notifications
    const notiFromId = latestNotifications?.find((item: any) => item?.id === id)

    let updatedNotifications = latestNotifications?.filter(
      (item: any) => item?.id !== id
    )

    let prevNotifications = [...previousNotifications]

    if (notiFromId) prevNotifications.push(notiFromId)

    dispatch(setPreviousNotifications(prevNotifications))
    dispatch(setLatestNotifications(updatedNotifications))
    saveNotificationsToStorage('previous', prevNotifications)
    saveNotificationsToStorage('latest', updatedNotifications)
  }

  /**
   * Clears a notification from the previous notifications list.
   *
   * @param id - The unique identifier of the notification to be cleared.
   *
   * This function removes the notification with the specified id from
   * the previous notifications list, updates the Redux store with the
   * new list, and saves the updated list to local storage.
   */

  const clearNotificationFromPrevious = (id: string) => {
    // handler func to clear notification from previous notifications
    let updatedNotifications = previousNotifications?.filter(
      (item: any) => item?.id !== id
    )
    dispatch(setPreviousNotifications(updatedNotifications))
    saveNotificationsToStorage('previous', updatedNotifications)
  }

  /**
   * Handler function to clear a single notification.
   *
   * @param id - The unique identifier of the notification to be cleared.
   *
   * This function determines which tab is currently active and then calls the
   * appropriate function to clear the notification from that tab. It also sets
   * the Redux store `isNewNotification` to false.
   */
  const handleCloseNotification = (id: string) => {
    // handler func to clear a single notification
    if (activeTab === 1) clearNotificationFromLatestUpdate(id)
    else if (activeTab === 2) clearNotificationFromPrevious(id)
    dispatch(setIsNewNotification(false))
  }

  /**
   * Clears all notifications from the latest updates and moves them to the previous notifications.
   *
   * This function takes all notifications currently in the latest updates list,
   * appends them to the previous notifications list, and then clears the latest
   * updates list. It updates the Redux store and local storage with the new lists.
   */

  const clearAllLatestNotification = () => {
    // handler func to clear all latest notifications and move to previous notifications
    let allLatestNotifications = [...latestNotifications]

    let allPreviousNotifications = [...previousNotifications]

    allPreviousNotifications = [
      ...allLatestNotifications,
      ...allPreviousNotifications,
    ]

    dispatch(setLatestNotifications([]))
    saveNotificationsToStorage('latest', [])
    dispatch(setPreviousNotifications(allPreviousNotifications))
    saveNotificationsToStorage('previous', allPreviousNotifications)
  }

  /**
   * Clears all notifications from the previous notifications list.
   *
   * This function empties the previous notifications list,
   * updates the Redux store, and clears the corresponding
   * entries in local storage.
   */

  const clearAllPreviousNotification = () => {
    // handler func to clear all previous notifications
    dispatch(setPreviousNotifications([]))
    saveNotificationsToStorage('previous', [])
  }

  /**
   * Handles closing all notifications.
   *
   * This function is called when the user clicks the "Clear All" button
   * in the Notification Center. It determines which tab is currently active
   * (latest updates or previous notifications) and calls the appropriate
   * function to clear all notifications from that tab. It also sets `isNewNotification`
   * to false in the Redux store and shows the confetti animation.
   */
  const handleCloseAllNotification = () => {
    //  handler func to clear all notifications
    setShowConfetti(true)

    if (activeTab === 1) clearAllLatestNotification()
    else if (activeTab === 2) clearAllPreviousNotification()
    dispatch(setIsNewNotification(false))
  }

  useEffect(() => {
    // false the confetti flag, as switching tab makes confetti to trigger again
    if (showConfetti) setShowConfetti(false)
  }, [showConfetti])

  /**
   * Handler function to close the notification center.
   *
   * This function sets the appropriate default tab based on whether the user is on Safari
   * and whether notifications are allowed. It also sets `isNewNotification` to false and
   * updates the Redux store, and then closes the notification center.
   */
  const handleNotiCenterClose = () => {
    //  handler func to close notification center
    // Set appropriate default tab based on Safari and notification permission
    if (isSafari) {
      setActiveTab(0) // Updates tab for Safari (since Preferences is hidden)
    } else if (!isNotificationAllowed) {
      setActiveTab(0) // Preferences tab for non-Safari when notifications not allowed
    } else {
      setActiveTab(1) // Updates tab for non-Safari when notifications are allowed
    }
    setIsPrefSaved(false)
    dispatch(setIsNewNotification(false))
    dispatch(setIsNotificationCenterOpen(false))
  }

  /**
   * Returns the appropriate tooltip text for the "Clear All" button
   * based on the currently active tab.
   *
   * @returns {string} The tooltip text for clearing all notifications,
   *                   specific to the active tab (latest updates or
   *                   previous notifications).
   */

  const getClearAllTooltip = () => {
    // get tooltip for clear all notifications
    if (activeTab === 1) return TooltipText.clearAllLatestNotifications
    else if (activeTab === 2) return TooltipText.clearAllPreviousNotifications
  }

  /**
   * Determines the vertical overflow behavior for the notification center
   * based on the currently active tab and notification state.
   *
   * @returns {string} The overflow-y CSS property value. It returns 'auto' if
   *                   the active tab has notifications or notifications are
   *                   allowed, otherwise 'hidden'. Defaults to 'auto' for other tabs.
   */

  const getOverflowY = () => {
    //  get overflow y for notification center
    if (activeTab === 0) {
      return isNotificationAllowed ? 'auto' : 'hidden'
    }

    if (activeTab === 1) {
      return latestNotifications?.length > 0 ? 'auto' : 'hidden'
    }
    if (activeTab === 2) {
      return previousNotifications?.length > 0 ? 'auto' : 'hidden'
    }
    return 'auto' // default for other tabs
  }

  // Animation Variants
  const animatedWrapperVariants = {
    hidden: { opacity: 0, scale: 0.7 },
    visible: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.7 },
  }

  // clear all notifications icon
  const ClearAllNotiIcon = !isActiveTabNotiZero() ? (
    <Tooltip
      title={
        isNotificationAllowed && !isActiveTabNotiZero()
          ? getClearAllTooltip()
          : ''
      }
      placement='bottom'
    >
      <div className={styles.iconWrapper}>
        <GenericIcon
          icon='CheckDouble'
          iconColour='cp1'
          bgColour={'bs1'}
          size='sm'
          isEnabled={isNotificationAllowed}
          htmlAttr={{
            className: `${styles.tabIcon} ${isNotificationAllowed && styles.checkIcon}`,
            onClick: (e: any) => {
              e.stopPropagation()
              handleDismiss()
            },
          }}
        />
      </div>
    </Tooltip>
  ) : (
    <></>
  )

  const PereferenceTab = {
    icon: (
      <Tooltip title={TooltipText.settingsNotifications} placement='bottom'>
        <div className={styles.iconWrapper}>
          <GenericIcon
            icon='Person'
            iconColour={activeTab == 0 ? 'cs1' : 'cp1'}
            // iconColour={'cn3'}
            size='md'
            htmlAttr={{
              className: styles.settingsIcon,
            }}
          />
        </div>
      </Tooltip>
    ),
    width: 'auto',
  }

  const UpdatesTab = {
    label: 'Updates',
    className: isNewUnNoticedNotification ? styles.pulsateTab : '',
    width: isSafari ? '50%' : '40%',
    labelIcon:
      activeTab === 1 ? (
        ClearAllNotiIcon
      ) : (
        <AnimatePresence>
          {isNewUnNoticedNotification && (
            <motion.div
              className={styles.animatedBorderWrapper}
              variants={animatedWrapperVariants}
              initial='hidden'
              animate='visible'
              exit='exit'
              transition={{ duration: 0.3, ease: 'easeOut' }}
            >
              <GenericIcon
                icon='Bell'
                iconColour='cp1'
                size='sm'
                isRounded
                htmlAttr={{ className: styles.animatedBell }}
              />
            </motion.div>
          )}
        </AnimatePresence>
      ),
  }

  const HistoryTab = {
    label: 'History',
    width: isSafari ? '50%' : '40%',
    labelIcon: activeTab === 2 ? ClearAllNotiIcon : null,
  }

  // tabs for notification center
  let tabs: any[] = []

  if (!isSafari) {
    tabs = [PereferenceTab, UpdatesTab, HistoryTab]
  } else {
    tabs = [UpdatesTab, HistoryTab]
  }

  /**
   * Stores default preferences in local storage and indexedDB.
   * @description
   * The default preferences are:
   * - allowedNotificationTypes: all types of notifications
   * - clearAfter: 'never' (keep previous notification forever)
   * - noExperience: 'false' (allow exit popup)
   * - noBrowserNoti: 'false' (allow browser notification)
   * - experience: 'yes' (allow exit popup)
   * - isSoundEnabled: 'true' (allow notification sound)
   * @private
   */
  const storeDefaultPrefs = () => {
    // store default preferences
    let allowedTypes = AllowedNotificationType?.map((item) => item?.value)

    allowedTypes.push('general')

    const prefs = {
      allowedNotificationTypes: allowedTypes,
      clearAfter: 'never', // interval to keep previous notification for given time
      noExperience: 'false', // used for exit popup control
      noBrowserNoti: 'false', // used for browser notification control
      experience: 'yes', // used for exit popup control
      isSoundEnabled: 'true', // used for notification sound control
    }
    setPreferences(prefs)
    dispatch(savePreferences(prefs))
    setLocalStorageItem('preferences', prefs)
    setTimeout(() => {
      syncToIndexedDB()
    }, PREF_SAVE_INTERVAL)
  }

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Handler function to save changes in notification preferences done by user
   * @param {Object} obj - Object with keys: category, value
   * @param {String} obj.category - category of preference to be updated
   * @param {String|Boolean} obj.value - value of preference to be updated
   * @description
   * Based on the category, the value is updated in the preferences object.
   * If category is 'allowedNotificationTypes', the value is an array of strings.
   * If the value is 'general', it either selects all allowed notification types
   * or unselects all if all are already selected.
   * If the value is any other string, it toggles the selection of that notification type.
   * For all other categories, the value is a boolean or string.
   * The updated preferences are stored in local storage and indexedDB.
   * @private
   */
  /*******  b8529bf4-9193-4139-bfb0-aecd352cf271  *******/
  const handleNotificationPreferences = ({
    category,
    value,
  }: {
    category: string
    value: string | boolean
  }) => {
    // handler func to save changes in notification preferences done by user
    let pref = { ...preferences }

    if (!category || typeof category !== 'string') {
      return
    }

    if (category === 'allowedNotificationTypes') {
      const allValues = AllowedNotificationType?.map((item) => item?.value)

      // Initialize if not present
      if (!pref.allowedNotificationTypes) {
        pref.allowedNotificationTypes = []
      }

      const currentTypes = pref?.allowedNotificationTypes

      if (value === 'general') {
        // If general is selected and not all are already selected, select all
        const areAllSelected = allValues?.every((type) =>
          currentTypes?.includes(type)
        )

        if (!areAllSelected) {
          pref.allowedNotificationTypes = [...allValues, 'general']
        } else {
          // Uncheck all
          pref.allowedNotificationTypes = []
        }
      } else {
        // Toggle individual type
        const updated = currentTypes?.includes(value)
          ? currentTypes?.filter((v: any) => v !== value)
          : [...currentTypes, value]

        const areAllNowSelected = allValues?.every((type) =>
          updated?.includes(type)
        )

        pref.allowedNotificationTypes = areAllNowSelected
          ? [...updated, 'general']
          : updated?.filter((v: any) => v !== 'general')
      }
    } else {
      pref[category] = value
    }

    // Update your state here
    setPreferences(pref)
    dispatch(savePreferences(pref))
    setLocalStorageItem('preferences', pref)
    setIsPrefSaved(true)
    setTimeout(() => {
      syncToIndexedDB()
      setIsPrefSaved(false)
    }, PREF_SAVE_INTERVAL)
  }

  let TabContentSafariBased = <></>

  const UpdatesTabContent = (
    <div
      style={{
        width: '100%',
        height: '100%',
        padding: '20px',
      }}
      className={styles.group}
    >
      {latestNotifications?.length > 0 ? (
        latestNotifications?.map((item, index) => (
          <AnimatePresence key={item?.id} mode='wait'>
            <motion.div
              initial={{ x: '100%', opacity: 0 }}
              animate={
                isSlidingOut ? { x: '100%', opacity: 0 } : { x: 0, opacity: 1 }
              }
              onAnimationComplete={() => {
                if (isSlidingOut) {
                  // Set completed flag when we reach either the last item (for small lists)
                  // or the 5th item (for larger lists)
                  const isLastItem = index === latestNotifications.length - 1
                  const isFifthItem = index === 5

                  if (
                    (latestNotifications.length <= 5 && isLastItem) ||
                    (latestNotifications.length > 5 && isFifthItem)
                  ) {
                    setIsClearAllAnimationCompleted(true)
                  }
                }
              }}
              exit={{
                opacity: 0,
                height: 0,
                marginBottom: 0,
                padding: 0,
              }}
              transition={{
                duration: 0.5,
                ease: 'easeInOut',
                delay: isSlidingOut ? index * 0.2 : 0,
              }}
              // style={{ overflow: 'hidden' }}
            >
              <CustomNotification
                isPreviousNotification={false}
                isPushNotification={false}
                isSysNotification={false}
                handleClose={() => handleCloseNotification(item?.id)}
                {...item}
              />
            </motion.div>
          </AnimatePresence>
        ))
      ) : (
        <NoNotificationDiv textContent='No new notifications, you are up-to-date!' />
      )}
    </div>
  )

  const HistoryTabContent = (
    <div
      style={{
        width: '100%',
        height: '100%',
        padding: '20px',
      }}
      className={styles.group}
    >
      {previousNotifications?.length > 0 ? (
        previousNotifications?.map((item, index) => (
          <AnimatePresence key={item?.id} mode='wait'>
            <motion.div
              initial={{ x: '100%', opacity: 0 }}
              animate={
                isSlidingOut ? { x: '100%', opacity: 0 } : { x: 0, opacity: 1 }
              }
              onAnimationComplete={() => {
                if (isSlidingOut) {
                  // Set completed flag when we reach either the last item (for small lists)
                  // or the 5th item (for larger lists)
                  const isLastItem = index === previousNotifications.length - 1
                  const isFifthItem = index === 5

                  if (
                    (previousNotifications.length <= 5 && isLastItem) ||
                    (previousNotifications.length > 5 && isFifthItem)
                  ) {
                    setIsClearAllAnimationCompleted(true)
                  }
                }
              }}
              exit={{
                opacity: 0,
                height: 0,
                marginBottom: 0,
                padding: 0,
              }}
              transition={{
                duration: 0.4,
                ease: 'easeInOut',
                delay: isSlidingOut ? index * 0.2 : 0,
              }}
              // style={{ overflow: 'hidden' }}
            >
              <CustomNotification
                {...item}
                isPreviousNotification={false}
                isPushNotification={false}
                isSysNotification={item?.isSystemNotification === 'true'}
                handleClose={() => handleCloseNotification(item?.id)}
              />
            </motion.div>
          </AnimatePresence>
        ))
      ) : (
        <NoNotificationDiv textContent='You have no new notifications.' />
      )}
    </div>
  )

  const PreferenceTabContent = (
    <NotificationPreferences
      preferences={preferences}
      isPrefSaved={isPrefSaved}
      handleNotificationPreferences={handleNotificationPreferences}
      isPermissionAllowed={isNotificationAllowed}
      handleAllowNotifications={handleAllowNotifications}
      isNotiPermissionLoading={isNotiPermissionLoading}
    />
  )

  if (isSafari) {
    TabContentSafariBased = (
      <FramerTabItem value={activeTab}>
        {UpdatesTabContent}
        {HistoryTabContent}
      </FramerTabItem>
    )
  } else {
    TabContentSafariBased = (
      <FramerTabItem value={activeTab}>
        {PreferenceTabContent}
        {UpdatesTabContent}
        {HistoryTabContent}
      </FramerTabItem>
    )
  }

  return (
    <AnimatePresence>
      {isNotificationCenterOpen && (
        <motion.div
          key='notification-center'
          className={styles.notificationCenterRoot}
          initial={{ right: '-600px' }}
          animate={{ right: 0 }}
          ref={notiRef}
          exit={{ right: '-600px' }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
        >
          <div className={styles.notificationCenter}>
            <div className={styles.notificationCenterBg}></div>
            <div className={styles.notificationCenterBgtop}></div>

            <div className={styles.content}>
              <div className={styles.headerRoot}>
                <div className={styles.blurLayer}></div>

                <div className={styles.headerContentRoot}>
                  <div className={styles.headerContent}>
                    <p className={styles.headerContentTitle}>
                      Your Altus Group Updates
                    </p>
                  </div>

                  <div className={styles.iconsGroup}>
                    <Tooltip
                      title={TooltipText.closeNotificationCenter}
                      placement='bottom'
                      arrow
                    >
                      <div className={styles.iconWrapper}>
                        <GenericIcon
                          icon='Close'
                          iconColour='cn3'
                          size='lg'
                          htmlAttr={{
                            className: styles.closeIcon,
                            onClick: () => handleNotiCenterClose(),
                          }}
                        />
                      </div>
                    </Tooltip>
                  </div>
                </div>
                <p className={styles.headerContentDescription}>
                  Your notification center for real time news from Altus Group
                </p>
              </div>
              <div>
                <Confetti
                  run={showConfetti}
                  ticks={100}
                  gravity={1.2}
                  angle={undefined} // to spread in all directions add as undefined
                  originY={0.1} // to spread from the top
                  originX={0.6} // to spread from the center
                  particleRatios={[0.25, 0.2, 0.35, 0.1, 0.1]} // to have different particle counts
                  startVelocityValues={[20, 30, undefined, 10, 15]} // to have different start velocities
                  spreadValues={[270, 270, 360, 360, 360]} // to have different spreads
                  decayValues={[undefined, undefined, 0.91, 0.92, undefined]} // to have different decays
                  scalarValues={[1, 1, 0.8, 1.2, 1]} // to have different scalars
                  colors={['#ffd700', '#0ff', '#ffdab9', '#f00', '#008000']}
                />
                <div className={styles.tabsRoot}>
                  <div className={styles.backdrop}></div>
                  <FramerTabsController
                    tabs={tabs}
                    activeTab={activeTab}
                    setActiveTab={(x: number) => {
                      setActiveTab(x)
                    }}
                  />
                </div>

                <div style={{ overflowY: getOverflowY() }}>
                  {TabContentSafariBased}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default NotificationCenter