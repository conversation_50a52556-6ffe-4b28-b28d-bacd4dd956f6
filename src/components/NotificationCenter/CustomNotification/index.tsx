import { Tooltip } from 'antd'
import { motion } from 'framer-motion'
import { useState } from 'react'
import { TooltipText } from '../../../globals/defaults'
import useTabFocus from '../../../hooks/useTabFocus'
import FramerBar from '../../Bars/FramerBar'
import SimpleButton from '../../CTAs/SimpleButton'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import { dismissGeoNotification } from '../GeoLocationNotification'
import { getTimeAgo } from '../utils'
import styles from './index.module.scss'

interface CustomNotificationI {
  isPushNotification?: boolean
  icon: string
  isPreviousNotification?: boolean
  title: string
  body?: string
  desc?: string
  timeStamp?: string
  url?: string
  ctaText?: string
  ctaTarget?: string
  duration?: string
  handleClick?: () => void
  handleClose: (arg: any) => void
  isSysNotification?: boolean
}

const CustomNotification = (props: CustomNotificationI) => {
  const [isPaused, setIsPaused] = useState(false)
  const [isSlidingOut, setIsSlidingOut] = useState(false)
  const isTabActive = useTabFocus()

  const handleMouseEnter = () => setIsPaused(true)
  const handleMouseLeave = () => setIsPaused(false)
  const handleDismiss = () => setIsSlidingOut(true)

  return (
    <motion.div
      className={
        props?.isPushNotification
          ? `${styles.notificationRoot} ${styles.borderTranslucent}`
          : styles.notificationRoot
      }
      animate={isSlidingOut ? { x: '100%', opacity: 0 } : { x: 0, opacity: 1 }}
      transition={{ duration: 0.5, ease: 'easeInOut' }}
      onAnimationComplete={() => {
        if (isSlidingOut) props?.handleClose(false)
      }}
    >
      <div
        className={
          props?.isPushNotification
            ? styles.pushNotification
            : styles.notification
        }
      >
        <div
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            props?.handleClick && props?.handleClick()
          }}
          className={styles.content}
        >
          <div className={styles.notificationWithIcon}>
            {/* {props?.isSystemNotification ? ( */}
            {/*   <span className={styles.settingsThumbnail}> */}
            {/*     <GenericIcon icon={'Settings'} size={'lg'} /> */}
            {/*   </span> */}
            {/* ) : ( */}
            {props?.isSysNotification ? (
              <span className={styles.thumbnailRootIcon}>
                <GenericIcon
                  icon={'Settings'}
                  size={'lg'}
                  iconColour={'cp1'}
                  htmlAttr={{ className: styles.thumbnailSettingsIcon }}
                />

                {/* <span className={styles.altusThumbnailRoot}> */}
                {/*   <GenericIcon */}
                {/*     icon={'Settings'} */}
                {/*     size={'lg'} */}
                {/*     htmlAttr={{ className: styles.thumbnailSettingsIcon }} */}
                {/*   /> */}
                {/* </span> */}
              </span>
            ) : (
              <span className={styles.thumbnailRoot}>
                <img
                  alt={'NotificationImage'}
                  className={`${styles.thumbnail} shadow3 rounded max`}
                  src={props?.icon}
                />
              </span>
            )}
            {/* )} */}

            <div
              className={`${props?.isSysNotification ? styles.sysInnerContent : styles.innerContent}`}
            >
              <p
                className={
                  props?.isPreviousNotification
                    ? `${styles.prevTitle} ${styles.title}`
                    : styles.title
                }
              >
                {props?.title}
              </p>
              <div className={styles.descriptionRoot}>
                <p
                  className={
                    props?.isPreviousNotification
                      ? `${styles.prevDescription} ${styles.description}`
                      : styles.description
                  }
                >
                  {props?.body || props?.desc}
                </p>
              </div>
              <div className={styles.btnRoot}>
                <p
                  className={
                    props?.isPreviousNotification
                      ? `${styles.prevTimeText} ${styles.timeText}`
                      : styles.timeText
                  }
                >
                  {getTimeAgo(props?.timeStamp || '')}
                </p>
                <div className={styles.btnGroup}>
                  <Tooltip
                    title={
                      props?.isPushNotification
                        ? ''
                        : TooltipText.clearNotification
                    }
                    placement='bottom'
                    arrow
                    color='#000'
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      {props?.isPushNotification ? (
                        <SimpleButton
                          variant={'tertiary3'}
                          textContent={'Close'}
                          htmlAttr={{
                            className: styles.closeLink,
                            onClick: (e: any) => {
                              e.stopPropagation()
                              e.preventDefault()
                              handleDismiss()
                              dismissGeoNotification()
                            },
                          }}
                        />
                      ) : (
                        <GenericIcon
                          icon={'Tickmark'}
                          iconColour={'cn3'}
                          size={'sm'}
                          htmlAttr={{
                            'data-tooltip-id': 'tooltip',
                            className: styles.checkIcon,
                            onClick: (e: any) => {
                              e.stopPropagation()
                              handleDismiss()
                            },
                          }}
                        />
                      )}
                    </div>
                  </Tooltip>
                  <SimpleButtonWIcon
                    variant={'tertiary'}
                    htmlAttr={{
                      className: props?.isPreviousNotification
                        ? `${styles.prevLink} ${styles.link}`
                        : styles.link,
                      onClick: (e: any) => {
                        if (!props?.url) return
                        e.preventDefault()
                        e.stopPropagation()
                        props?.handleClose &&
                          props?.handleClose({ isReadMoreClick: true })
                        window.open(props?.url, props?.ctaTarget || '_self')
                      },
                    }}
                    isChevron2Arrow
                    textContent={props?.ctaText || 'Read more'}
                  />
                </div>
              </div>
            </div>
          </div>
          {props?.isPushNotification &&
            props?.duration &&
            props?.duration !== '0' && (
              <FramerBar
                duration={Number(props?.duration)}
                isPaused={isPaused || !isTabActive}
                onComplete={() => handleDismiss()}
              />
            )}
        </div>
      </div>
    </motion.div>
  )
}

export default CustomNotification