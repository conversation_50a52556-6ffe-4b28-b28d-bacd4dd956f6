.borderTranslucent {
  padding: 3px;
  background-color: $cs2;
  }

// @keyframes progress1 {
//   0% {
//     transform: scalex(0);
//     opacity: .5;
//   }
//   90% {
//     transform: scalex(1);
//     opacity: 1;
//   }
//   92% {
//     transform: scalex(1);
//     opacity: 1;
//   }
//   100% {
//     transform: scalex(1);
//     opacity: 0;
//   }
// }
//
// @keyframes progress2 {
//   0% {
//     transform: scale(.3,.8) translatez(0);
//     opacity: 0;
//   }
//   90% {
//     transform: scale(1,1) translatex(300px) translatez(0);
//     opacity: 1;
//   }
//   100% {
//     transform: scale(1,1) translatex(300px) translatez(0);
//     opacity: 0;
//   }
// }

.notificationRoot {
  // margin-bottom: 20px;
  position: relative;
  width: 395px;
  border-radius: 8px;
  transition: all 0.25s ease;
  // border: 1px solid transparent;

  @media (max-width: $smScreenSize) {
    width: 370px;
    }

  &:hover.borderTranslucent {
    background-color: $cp1;
    }

  .pushNotification {
    position: relative;
    width: 100%;
    overflow: hidden;
    word-wrap: break-word;
    // border: 1px solid rgba(255, 255, 255, 0.5);
    background: $cp1;
    z-index: 1;
    border-radius: 8px;
    transition: all 0.25s ease;
    // box-shadow: rgba(50, 50, 93, 0.25) 0px 30px 60px -12px, rgba(0, 0, 0, 0.3) 0px 18px 36px -18px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;

    .sysInnerContent {
      width: 100%;
      padding: 20px 20px 20px 0;
      }

    .content {
      // padding-top: 20px;
      // border-top-left-radius: 8px;
      // border-top-right-radius: 8px;
      border-radius: 8px;
      backdrop-filter: blur(2rem);
      transition: all 0.25s ease;


      .innerContent {
        width: 100%;
        padding: 20px 20px 20px 0;
        }

      .notificationWithIcon {
        display: flex;
        transition: all 0.25s ease;
        // padding: 0 20px 20px 20px;

        .thumbnailRootIcon {
          // margin-top: -16px;
          // margin-left: -5px;
          transition: all 0.25s ease;
          // background-color: $cn8;

          .thumbnailSettingsIcon {
            svg {
              color: $cn8;
              }
            }
          }

        .thumbnailRoot {
          margin-top: -5px;
          margin-left: -5px;
          transition: all 0.25s ease;
          padding: 20px 0 20px 20px;
          }

        .btnRoot {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 5px;
          height: 10px;
          transition: all 0.25s ease;

          .timeText {
            font-size: 0.8rem;
            color: $cn5;

            }

          .btnGroup {
            display: flex;
            align-items: center;
            gap: 20px;

            .closeLink {
              // color: $cp1;
              font-family: $fSansReg !important;
              font-size: 14px !important;
              letter-spacing: 0.02em;
              border-radius: 5px !important;
              text-box: trim-both cap alphabetic;
              justify-content: end !important;
              min-width: fit-content !important;
              transition: all 0.25s ease;

              span {
                gap: 4px !important;
                }
              }
            }
          }

        .title {
          font-size: 16px;
          color: $cs1;
          line-height: 1.3;
          margin-bottom: 14px;
          letter-spacing: 0.02em;
          text-box: trim-both cap alphabetic;
          transition: all 0.25s ease;
          }

        .link {
          color: $cs1;
          font-family: $fSansReg;
          font-size: 14px;
          letter-spacing: 0.02em;
          border-radius: 5px;
          text-box: trim-both cap alphabetic;
          transition: all 0.25s ease;
          justify-content: end !important;
          min-width: fit-content !important;

          span {
            gap: 4px !important;
            }
          }

        .thumbnail {
          width: 50px;
          height: 50px;
          margin-right: 12px;
          border: 1px solid white;
          box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px,
          rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
          transition: all 0.25s ease;
          }
        }

      .progressBar {
        background-color: $cp2;

        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        height: 3px;

        // &:before {
        //   animation: progress1 4s infinite;
        //   transform-origin: 0 0;
        //   content: "";
        //   display: block;
        //   width: 100%;
        //   height: 100%;
        //   background: linear-gradient(to right, rgba(255, 255, 255, 0.1) 10%, rgba(255, 255, 255, 0.4) 80%, rgba(255, 255, 255, 1));
        //   }
        //
        // &:after {
        //   content: "";
        //   position: absolute;
        //   animation: progress2 4s infinite;
        //   transform-origin: 90% 50%;
        //   margin-left: 60px;
        //   top: 0;
        //   width: 30px;
        //   height: 21px;
        //   border-radius: 2px;
        //   background: rgba(210, 189, 255, .55);
        //   filter: blur(8px);
        //   box-shadow: 0 0 10px 6px rgba(210, 189, 255, .4),
        //   -20px 0 15px 4px rgba(210, 189, 255, .3),
        //   -40px 0 15px 2px rgba(210, 189, 255, .2),
        //   -60px 0 10px 1px rgba(210, 189, 255, .1),
        //   -80px 0 10px 1px rgba(210, 189, 255, .05);
        //   }
        }


      .descriptionRoot {
        position: relative;
        display: block;
        transition: all 0.25s ease;

        .description {
          font-family: $fSansReg;
          font-size: 0.85rem;
          line-height: 1.3;
          letter-spacing: 0.02em;
          color: $cs2;
          margin-bottom: 24px;

          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;

          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal;
          word-break: break-word;
          hyphens: auto;

          max-height: 3.2em;
          transition: all 0.3s ease;
          position: relative;
          }
        }
      }

    &:hover {
      // border: 1px solid $cs1;

      // box-shadow:
      //   rgba(50, 50, 93, 0.25) 0px 13px 27px -5px,
      //   rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
      // transition: all 0.25s ease;
      background-color: $cs2;

      // .thumbnailRootIcon {
      //   background-color: $cn5 !important;
      //   transition: all 0.25s ease;
      //   }

      .thumbnailRootIcon {
        .thumbnailSettingsIcon {
          svg {
            color: $cp1 !important;
            }
          }
        }

      .notification {
        transition: all 0.25s ease;
        // padding: 5px;


        .content {
          // background: $cs2;
          backdrop-filter: blur(2rem);
          }

        }

      .description {
        color: $cn1 !important;
        transition: all 0.25s ease;
        }

      .progressBar {
        background-color: $cp2;
        }

      .timeText {
        color: $cn3 !important;
        transition: all 0.25s ease;
        font-size: 0.75rem;
        }

      .title {
        color: $cp2 !important;
        transition: all 0.25s ease;
        // font-family: $fSansBld !important;
        }

      .link {
        color: $cp2 !important;
        transition: all 0.25s ease;
        font-family: $fSansBld !important;
        }

      .closeLink {
        color: $cn1 !important;
        transition: all 0.25s ease;
        font-family: $fSansBld !important;
        }


      }
    }

  .notification {
    position: relative;
    width: 100%;
    overflow: hidden;
    word-wrap: break-word;
    // padding: 5px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    background: radial-gradient(
                    at center top,
                    rgba(255, 255, 255, 1),
                    rgba(255, 255, 255, 0.25)
    );
    z-index: 1;
    border-radius: 8px;
    transition: all 0.25s ease;
    // box-shadow: rgba(50, 50, 93, 0.25) 0px 50px 100px -20px, rgba(0, 0, 0, 0.3) 0px 30px 60px -30px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;


    .checkIcon {
      margin-left: -5px;
      display: none;
      transition: all 0.25s ease;
      }

    .content {
      // padding-top: 20px;
      // border-top-left-radius: 8px;
      // border-top-right-radius: 8px;
      border-radius: 8px;
      backdrop-filter: blur(2rem);
      transition: all 0.25s ease;

      .sysInnerContent {
        width: 100%;
        padding: 20px;
        }

      .innerContent {
        width: 100%;
        padding: 20px 20px 20px 0;
        }

      .notificationWithIcon {
        display: flex;
        transition: all 0.25s ease;
        // padding: 20px;

        .thumbnailRootIcon {
          margin-top: -16px;
          // margin-left: -5px;
          transition: all 0.25s ease;
          }

        .thumbnailRoot {
          margin-top: -5px;
          margin-left: -5px;
          transition: all 0.25s ease;
          }

        .btnRoot {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 5px;
          height: 10px;
          transition: all 0.25s ease;

          .timeText {
            font-size: 0.8rem;
            color: $cn2;
            }

          .btnGroup {
            display: flex;
            align-items: center;
            }
          }

        .title {
          font-size: 16px;
          color: $cp1;
          line-height: 1.3;
          margin-bottom: 14px;
          letter-spacing: 0.02em;
          text-box: trim-both cap alphabetic;
          transition: all 0.25s ease;
          }

        .link {
          color: $cp1;
          font-family: $fSansReg;
          font-size: 14px;
          letter-spacing: 0.02em;
          border-radius: 5px;
          text-box: trim-both cap alphabetic;
          transition: all 0.25s ease;

          span {
            gap: 4px !important;
            }
          }

        .thumbnailRootIcon {
          position: relative;
          padding: 20px 0 20px 0;
          background-color: $cn5;
          }

        .thumbnailRoot {
          position: relative;
          padding: 20px 0 20px 20px;
          height: fit-content;

          .thumbnail {
            width: 50px;
            height: 50px;
            margin-right: 12px;
            border: 1px solid white;
            background-color: white;
            box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px,
            rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
            transition: all 0.25s ease;
            }

          .altusThumbnailRoot {
            background-color: white;
            position: absolute;
            width: 22px;
            height: 22px;
            bottom: -6px;
            right: 12px;
            border: 1px solid #ccc;
            border-radius: 50px;

            .thumbnailSettingsIcon {
              width: 100%;
              height: 100%;
              padding: 3px;
              }

            .altusThumbnail {
              width: 100%;
              height: 100%;
              padding: 3px;
              }
            }
          }

        }

      .descriptionRoot {
        position: relative;
        display: block;
        transition: all 0.25s ease;
        }

      .description {
        font-family: $fSansReg;
        font-size: 0.85rem;
        line-height: 1.3;
        letter-spacing: 0.02em;
        color: $cn3;
        margin-bottom: 24px;

        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;

        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        word-break: break-word;
        hyphens: auto;

        max-height: 3.2em;
        transition: all 0.3s ease;
        position: relative;
        }
      }

    .prevTitle, .prevTimeText, .prevDescription {
      color: $cn3 !important;
      }
    }

  &:hover {
    // border: 1px solid $cs1;

    // box-shadow:
    //   rgba(50, 50, 93, 0.25) 0px 13px 27px -5px,
    //   rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    // transition: all 0.25s ease;

    .notification {
      transition: all 0.25s ease;
      box-shadow: none;
      // padding: 5px;


      .content {
        background: $cp1;
        backdrop-filter: blur(2rem);
        }

      .checkIcon {
        display: flex;
        transition: all 0.25s ease;
        }
      }

    .description {
      color: $cs2 !important;
      transition: all 0.25s ease;
      }

    .timeText {
      color: $cn5 !important;
      transition: all 0.25s ease;
      font-size: 0.75rem;
      }

    .title {
      color: $cs1 !important;
      transition: all 0.25s ease;
      // font-family: $fSansBld !important;
      }

    .link {
      color: $cs1 !important;
      transition: all 0.25s ease;
      font-family: $fSansBld !important;
      }


    .checkIcon svg {
      color: $cs2 !important;
      transition: all 0.25s ease;
      }
    }
  }

.disabled {
  cursor: not-allowed;
  opacity: 0.5;
  transition: all 0.25s ease;
  }

.settingsThumbnail {
  margin-top: -17px !important;
  margin-left: -17px !important;
  }