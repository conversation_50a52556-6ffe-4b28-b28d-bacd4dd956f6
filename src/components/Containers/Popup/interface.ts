// import { KernelI } from 'src/components/Kernel';
import { ContextualInformationI } from '../../ContentBlocks/ContextualInformation/interface'
import { KernelI } from '../../Kernel'

/**
 * Popup interface enlists all the props (and their types) that <PERSON><PERSON> can have.
 */
export interface PopupI extends KernelI {
  width?: string
  height?: string
  popupTitle?: string
  /**
   * if true, close popup on click outside
   */
  isClosable?: boolean
  /**
   * This props is only for developer purpose
   */
  popupTitleClassName?: string

  isMaxedOut?: boolean

  ///**
  // * if ture, show border
  // */
  hasBorder?: boolean

  isShow?: boolean
  handleShow?: (e: any) => void
  contextualInformation?: ContextualInformationI
  isOverlay?: boolean
}
