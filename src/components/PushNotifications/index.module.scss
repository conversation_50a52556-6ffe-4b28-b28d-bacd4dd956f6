.pushNotificationsContainer {
  position: fixed;
  right: 20px;
  top: 20px;
  z-index: 99;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  height: auto;

  // Hide scrollbar for WebKit browsers (Chrome, Safari)
  &::-webkit-scrollbar {
    width: 0px;
    background: transparent;
    }

  // Hide scrollbar for Firefox
  scrollbar-width: none;

  // Hide scrollbar for IE/Edge
  -ms-overflow-style: none;

  @media (max-width: $smScreenSize) {
    right: 10px;
    }

  }