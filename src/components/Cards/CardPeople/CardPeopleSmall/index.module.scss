//Default SCSS variables are prepended in next.config.js

.cardProfile {
  display: grid;
  grid-template-columns: 60px 1fr;
  /* 2 columns with 150px width for the avatar */
  grid-gap: 10px;
  max-width: 390px;
  width: 100%;
  //height: 60px;

  .profileInfo {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
      border-radius: 50%;
      }
    }

  .details {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-grow: 1;
    margin-left: 14px;
    }

  /*  &:hover {
     .heading {
       color: $cp2;
       }

     .headingDark {
       color: $cs1;
       }
     } */
  }

.profilePhoto {
  width: 60px;
  height: 60px;
  }

.heading {
  //line-height: 26px;
  transition: color 0.3s;
  color: $cn2;
  }

.headingDark {
  // line-height: 26px;
  transition: color 0.3s;
  color: $cn8;
  /* Change text color on hover */
  }

.jobTitle {
  font-family: $fSansReg;
  font-size: 16px;
  line-height: 1.3;
  margin-top: 2px;
  }

.cta {
  &:hover {
    text-decoration: none;
    }
  }

.hoverClass:hover {
  .heading {
    color: $cp2;
    }

  .headingDark {
    color: $cs1;
    }
  }

.nohover {
  cursor: default !important;

  &:hover {
    cursor: default !important;
    }
  }