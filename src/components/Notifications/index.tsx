'use client'

import GeoLocationNotification from '../NotificationCenter/GeoLocationNotification'
import RealTimeLiveNotification from '../PushNotifications'

/**
 * A component that renders all the different types of notifications.
 *
 * Includes:
 * - BellAudioPlayer: plays a bell sound when a new notification is received
 * - RealTimeLiveNotification: fetches and displays live notifications
 * - NotificationCenter: displays all notifications
 * - GeoLocationNotification: displays a notification based on geolocation
 *
 * @returns {JSX.Element}
 */
function NotificationsWithAllComps() {
  return (
    <>
      {/* <BellAudioPlayer /> */}
      <RealTimeLiveNotification />
      {/* <NotificationCenter /> */}
      <GeoLocationNotification />
    </>
  )
}

export default NotificationsWithAllComps
