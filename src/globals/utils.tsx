import moment from 'moment'
import { SimpleCTAsI } from '../components/CTAs/SimpleCTAs/interface'
import deDictionary from '../lib/dictionaries/de-DE.json'
import enDictionary from '../lib/dictionaries/en-CA.json'
import esDictionary from '../lib/dictionaries/es.json'
import frDictionary from '../lib/dictionaries/fr-CA.json'
import itDictionary from '../lib/dictionaries/it.json'
import nlDictionary from '../lib/dictionaries/nl.json'
import { setPageScroll } from '../redux/slices/pageSlice'
import store from '../redux/store'
import { DOMAIN_TAGS } from '../utils'
import { DateTimeFormatOptions, GetDateAndTimeOutputI } from './types'

export const BUISNESS_DOMAINS = {
  altus: 'domainAltusGroupCom',
  finance: 'domainFinanceActiveCom',
  verifino: 'domainVerifinoCom',
  one11com: 'domainOne11Com',
  reonomycom: 'domainReonomyCom',
  agStudio: 'domainAgStudio',
}

export const BUISNESS_DOMAINS_FAVICONS = {
  domainAltusGroupCom: {
    apple: '/favicon/Altus/apple-touch-icon.png',
    favicon32: '/favicon/Altus/favicon-32x32.png',
    favicon16: '/favicon/Altus/favicon-16x16.png',
    site: '/favicon/Altus/site.webmanifest',
    safari: '/favicon/Altus/safari-pinned-tab.svg',
  },
  domainFinanceActiveCom: {
    apple: '/favicon/Finance/apple-touch-icon.png',
    favicon32: '/favicon/Finance/favicon-32x32.png',
    favicon16: '/favicon/Finance/favicon-16x16.png',
    site: '/favicon/Finance/site.webmanifest',
    safari: '/favicon/Finance/safari-pinned-tab.svg',
  },
  domainVerifinoCom: {
    apple: '/favicon/Verifino/apple-touch-icon.png',
    favicon32: '/favicon/Verifino/favicon-32x32.png',
    favicon16: '/favicon/Verifino/favicon-16x16.png',
    site: '/favicon/Verifino/site.webmanifest',
    safari: '/favicon/Verifino/safari-pinned-tab.svg',
  },
  domainOne11Com: {
    apple: '/favicon/one11/apple-touch-icon.png',
    favicon32: '/favicon/one11/favicon-32x32.png',
    favicon16: '/favicon/one11/favicon-16x16.png',
    site: '/favicon/one11/site.webmanifest',
    safari: '/favicon/one11/safari-pinned-tab.svg',
  },
  domainReonomyCom: {
    apple: '/favicon/reonomy/apple-touch-icon.png',
    favicon32: '/favicon/reonomy/favicon-32x32.png',
    favicon16: '/favicon/reonomy/favicon-16x16.png',
    site: '/favicon/reonomy/site.webmanifest',
    safari: '/favicon/reonomy/safari-pinned-tab.svg',
  },
  domainForburyCom: {
    apple: '/favicon/Forbury/apple-touch-icon.png',
    favicon32: '/favicon/Forbury/favicon-32x32.png',
    favicon16: '/favicon/Forbury/favicon-16x16.png',
    site: '/favicon/Forbury/site.webmanifest',
    safari: '/favicon/Forbury/safari-pinned-tab.svg',
  },
}
export const BUISNESS_DOMAINS_LOGOS = {
  domainAltusGroupCom: {
    'en-CA': '/logo/Altus/english/Altus-Group-logo.svg',
    'fr-CA': '/logo/Altus/french/Altus-Group-logo-FR.svg',
    'de-DE': '/logo/Altus/english/Altus-Group-logo.svg',
  },
  domainFinanceActiveCom: {
    'en-CA': '/logo/Finance/english/Finance-Active-Logo.svg',
    'fr-CA': '/logo/Finance/english/Finance-Active-Logo.svg',
    'de-DE': '/logo/Finance/english/Finance-Active-Logo.svg',
  },
  domainVerifinoCom: {
    'en-CA': '/logo/Verifino/english/Verifino-Logo.svg',
    'fr-CA': '/logo/Verifino/english/Verifino-Logo.svg',
    'de-DE': '/logo/Verifino/english/Verifino-Logo.svg',
  },
  domainOne11Com: {
    'en-CA': '/logo/one11/english/One11-Logo.svg',
    'fr-CA': '/logo/one11/english/One11-Logo.svg',
    'de-DE': '/logo/one11/english/One11-Logo.svg',
  },
  domainReonomyCom: {
    'en-CA': '/logo/reonomy/english/Reonomy-Logo.svg',
    'fr-CA': '/logo/reonomy/english/Reonomy-Logo.svg',
    'de-DE': '/logo/reonomy/english/Reonomy-Logo.svg',
  },
}
export const BUISNESS_DOMAINS_LOCALE_NAVIGATION: Record<
  string,
  Array<string>
> = {
  domainAltusGroupCom: ['en-CA', 'fr-CA'],
  domainFinanceActiveCom: ['en-CA', 'fr-CA', 'de-DE', 'es', 'nl', 'it'],
  domainVerifinoCom: ['en-CA', 'de-DE'],
  domainOne11Com: ['en-CA', 'fr-CA'],
  domainReonomyCom: ['en-CA', 'fr-CA'],
  domainForburyCom: ['en-CA'],
  domainAgStudio: ['en-CA'],
}

export const BUSINESS_DOMAINS_TITLE = {
  domainAltusGroupCom: 'Altus Group',
  domainFinanceActiveCom: 'Finance Active',
  domainVerifinoCom: 'Verifino',
  domainOne11Com: 'One 11',
  domainReonomyCom: 'Reonomy',
  domainForburyCom: 'Forbury',
  domainAgStudio: 'ADX',
}

export const TAG_TO_URL = {
  domainAltusGroupCom: 'https://www.altusgroup.com',
  domainVerifinoCom: 'https://www.verifino.com',
  domainFinanceActiveCom: 'https://financeactive.com',
  domainReonomyCom: 'https://www.reonomy.com',
  domainOne11Com: 'https://www.one11advisors.com',
  domainForburyCom: 'https://www.forbury.com',
  domainAgStudio: 'https://ag.studio',
}

export const LOCALE_CONSTANT: any = {
  en: 'en-CA',
  fr: 'fr-CA',
  de: 'de-DE',
  es: 'es',
  it: 'it',
  nl: 'nl',
}

/**
 * Add a node to the HTML Head tag
 * @param {Node} node - The node to be added to the head tag.
 */
export const add2Head = (node: Node) => {
  // Find the HTML head element in the document
  const head = document.querySelector('head') as HTMLHeadElement

  // Append the given node to the head element
  head.appendChild(node)
}

/**
 * Check if the code is running in a browser environment.
 * @returns {boolean} - Returns true if the code is running in a browser, false otherwise.
 */
export const isBrowser = () => typeof window !== 'undefined'

/**
 * Scroll the window to the top of the page.
 */
export const scrollToTop = () => {
  // Scroll the window to the top (x=0, y=0)
  window.scrollTo(0, 0)
}

export function TheSEOBlock({ seoObj }: any) {
  return (
    <>
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{ __html: JSON.stringify(seoObj) }}
      />
    </>
  )
  /**
   * @todo fix structured data schemas for components. Disabled until then.
   * <script type="application/ld+json" dangerouslySetInnerHTML={{__html: JSON.stringify(seoObj)}}/>
   */
}

export function registerScrollEvent() {
  // on dom ready add event listner update page progress on scroll
  window.onscroll = function () {
    // calculate scroll percentage
    const scrollPercentage =
      (document.documentElement.scrollTop /
        (document.documentElement.scrollHeight -
          document.documentElement.clientHeight)) *
      100
    l('scrollPercentage', scrollPercentage)
    // dispatch scroll percentage to redux store
    store.dispatch(setPageScroll(scrollPercentage))
  }
}

/**
 * Function to truncate the description to a specified word limit
 * @param text Specify the text that needs to be truncated.
 * @param limit Specify the number of words to which the text needs to be shortend
 * @param showEllipsis Specify whether and elipses should be shown at the end of truncated text.
 */
export function truncateText(
  text: string,
  limit: number,
  showEllipsis?: boolean
): string {
  let updatedText = text
  const words = text.split(' ')
  if (words.length > limit) {
    const ellipsis = showEllipsis === true ? '...' : ''
    updatedText = words.slice(0, limit).join(' ') + ellipsis
  }
  return updatedText
}

/**
 * Function to Get Time, Date and Timezone.
 * @param start this will take start date or time in string (e.g "2023-10-19T00:00:00.000Z" OR "2023-10-10T10:00:00.000+05:30")
 * @param end this will take end date or time in string
 */

export const getDateAndTime = (
  start: string,
  end: string | null
): GetDateAndTimeOutputI => {
  // converting strings into Date type
  const startDateAndTime = new Date(start)
  const endDateAndTime = end !== null ? new Date(end) : ''

  // options related to Time, Date and TimeZone
  const dateOption: DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }
  const timeOptions: DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  }

  // Code below converts date into these format
  // Date format is MMM/DD/YYYY =  Sep 30, 2023
  // If event duration extends more than 1 day, date format is Sep 29 - 30, 2023

  const startDate = startDateAndTime.toLocaleDateString('en-US', {
    ...dateOption,
    year: !endDateAndTime ? 'numeric' : undefined,
  })
  const endDate = endDateAndTime
    ? endDateAndTime.toLocaleDateString('en-US', {
      ...dateOption,
    })
    : null

  // Code below converts time into these format
  // Time format is HH:MM AM/PM =  04:30 AM
  // If event time contains Start and End time, time format is 9:00 - 5:00 PM

  const startTime = startDateAndTime.toLocaleTimeString('en-US', timeOptions)
  const endTime = endDateAndTime
    ? endDateAndTime.toLocaleTimeString('en-US', timeOptions)
    : null

  // Code below return Timezone in string type
  const timeZoneAbbreviation = (
    Intl.DateTimeFormat('en-US', { timeZoneName: 'short' })
      .formatToParts(startDateAndTime)
      .find((part) => part.type === 'timeZoneName') || {}
  ).value

  const Result = {
    date: ` ${startDate} ${endDate !== null ? '- ' + endDate : ''}`,
    time: ` ${endTime !== null ? startTime.split(' ')[0] : startTime} ${endTime !== null ? '- ' + endTime : ''
      } `,
    timeZone: timeZoneAbbreviation,
  }
  return Result
}

/**
 * This function use for divide the original array into smaller subarrays of
 * the specified size
 * @param array - original array
 * @param size - chunk size
 * @returns
 */

function chunkArray<T>(array: T[], size: number) {
  return Array.from({ length: Math.ceil(array.length / size) }, (_, i) =>
    array.slice(i * size, i * size + size)
  )
}

export default chunkArray

/**
 * Function to covert slug to title
 * @param slug Specify the text that needs to be Title.
 * @returns normal title
 */
export function makeTitle(slug: string, locale: string = 'en-CA') {
  if (!slug) {
    return useTranslation('home', locale)
  }
  var words = slug.split('-')

  for (var i = 0; i < words.length; i++) {
    var word = words[i]
    words[i] = word.charAt(0).toUpperCase() + word.slice(1)
  }

  return words.join(' ')
}

/**
 * Funtion to navigate to the specified URL
 * @param url Specify the url.
 */
export const RouteToUrl = (
  url: string,
  target: SimpleCTAsI['target'] = '_self'
) => {
  if (!isBrowser()) return
  if (url === '#') return
  if (target === '_blank') {
    window.open(url, '_blank')
  } else {
    location.href = url
  }
}
// Get an item from localStorage
export const getLocalStorageItem = (key) => {
  try {
    const serializedItem = localStorage.getItem(key)
    return JSON.parse(serializedItem)
  } catch (error) {
    l(`Error getting item from localStorage: ${error.message}`)
    return null
  }
}

// Set an item in localStorage
export const setLocalStorageItem = (key, value) => {
  try {
    const serializedItem = JSON.stringify(value)
    localStorage.setItem(key, serializedItem)
  } catch (error) {
    l(`Error setting item in localStorage: ${error.message}`)
  }
}

// Remove an item from localStorage
export const removeLocalStorageItem = (key) => {
  try {
    localStorage.removeItem(key)
  } catch (error) { }
}

// Set an item in sessionStorage
export const setSessionStorageItem = (key: string, val) => {
  try {
    sessionStorage.setItem(key, val)
  } catch (e) { }
}

// Get an item in sessionStorage
export function getSessionStorageItem(key: string) {
  let item = null
  try {
    item = sessionStorage.getItem(key)
  } catch (e) { }
  return item
}

// Remove an item from sessionStorage
export const removeSessionStorageItem = (key: string) => {
  try {
    sessionStorage.removeItem(key)
  } catch (error) { }
}

export const getQueryParamJson = () => {
  if (!isBrowser()) return {}

  // Parse query parameters into an object
  const params = decodeURIComponent(window?.location?.search?.slice(1) || '')
    .split('&')
    .map((param) => param.split('='))
    .reduce((acc, [key, value]) => {
      if (value) {
        acc[key] = value
      }
      return acc
    }, {})

  return params
}

// function to check if an item is already in the algusSearchHistory local storage
export const checkIsAlreadyInHistory = (inputValue) => {
  const existingSearchHistory = getLocalStorageItem('altusSearchHistory') || []

  // Check if inputValue is not already in the search history
  const isItemAlreadyInHistory = existingSearchHistory.some(
    (item: { textContent: string; href: string }) =>
      item.textContent === inputValue.textContent &&
      item.href === inputValue.href
  )

  return { existingSearchHistory, isItemAlreadyInHistory }
}

// functions for setting recent searches inside the local storage
export const setRecentSearches = (inputValue: {
  textContent: string
  href: string
}) => {
  const { existingSearchHistory, isItemAlreadyInHistory } =
    checkIsAlreadyInHistory(inputValue)

  if (!isItemAlreadyInHistory) {
    // Add the inputValue to the search history
    existingSearchHistory.unshift(inputValue)

    // Sanitize the array by excluding elements with circular references
    const sanitizedSearchHistory = existingSearchHistory.map((item) =>
      // Check if the item is a primitive value
      typeof item === 'object' && item !== null
        ? { ...item } // Create a shallow copy of the object
        : item
    )

    setLocalStorageItem('altusSearchHistory', sanitizedSearchHistory)
  }
}

// Function to scroll to an element
export const handleElementScrollIntoView = (
  elementSelector,
  offsetMobile = 60,
  offsetDesktop = 305
) => {
  const targetElement = document.querySelector(elementSelector)

  if (targetElement) {
    const isMobile = window.innerWidth <= 768
    const offset = isMobile ? offsetMobile : offsetDesktop

    const top =
      targetElement.getBoundingClientRect().top + window.scrollY - offset

    window.scrollTo({
      top,
      behavior: 'smooth',
    })
  }
}

/**
 * Converts a string in the format "Country: United States|Insights: Article" to camelCase.
 * Only the first word is in lowercase, and subsequent words are in uppercase.
 * @param input - The input string to be converted.
 * @returns The camelCase representation of the input string.
 */
export function convertStringToCamelCase(input: string): string {
  if (DOMAIN_TAGS[input]) return DOMAIN_TAGS[input]

  // Split the input into words using regex that matches colons, periods, or spaces, and removes any non-alphanumeric characters
  const words = input.split(/[:.\s]/)

  // Convert the array of words into camelCase format
  const camelCaseString = words
    .map((word, index) => {
      // Remove leading and trailing non-alphanumeric characters from each word (preserving inner characters as they are)
      const cleanWord = word.replace(/^[^a-zA-Z0-9]+|[^a-zA-Z0-9]+$/g, '')

      if (index === 0) {
        // Entirely lowercase for the first word
        return cleanWord.toLowerCase()
      } else {
        // For subsequent words, capitalize the first letter without altering the rest of the word
        return (
          cleanWord.charAt(0).toUpperCase() + cleanWord.slice(1).toLowerCase()
        )
      }
    })
    .join('') // Join the words back together

  return camelCaseString
}

const enableLogging = process.env.NODE_ENV === 'development'
const enableVerbosity = true

let count = 0

export function l(...params: any) {
  if (enableLogging) {
    if (enableVerbosity) {
    }
    count++
  }
}

// Function to check if value exist in given objects
export const valueExists = (data: any, key: string, value: string) => {
  return data[key] === value
}

/**
 * Extracts values from a string containing key-value pairs separated by ":"
 * @param inputString - The input string containing key-value pairs
 * @returns A comma-separated string of values extracted from the input string, or the original string if no values are found
 */
export function extractValues(inputString: string): string {
  // Split the input string into an array using ":"
  const parts: string[] = inputString.split(':')

  const values: string[] = []

  // Iterate through the parts and extract values
  for (let i = 1; i < parts.length; i += 2) {
    // Extract the value and trim whitespace
    const value: string = parts[i].trim()

    // Push the value into the values array
    values.push(value)
  }

  // Check if values were found, otherwise return the original string
  if (values.length === 0) {
    return inputString
  }

  // Join the values into a comma-separated string
  const result: string = values.join(', ')

  return result
}

// Function to sort data in ascending order based on the 'date' property
export const sortedAsc = (data: any) => {
  let sorted = data.sort((a, b) => moment(a.date).diff(moment(b.date)))

  return sorted
}

// Function to sort data in descending order based on the 'date' property
export const sortedDesc = (data: any) => {
  let sorted = data.sort((a, b) => moment(b.date).diff(moment(a.date)))

  return sorted
}

export const componentRouterError = ({ text }: object) => {
  return {
    isPersistent: true,
    simpleParagraph: {
      data: {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            attrs: {
              textAlign: 'left',
            },
            content: [
              {
                type: 'text',
                text: text,
              },
            ],
          },
        ],
      },
    },
  }
}

/**
 * Function to find all objects with a specific key-value pair recursively.
 * @param {object} obj - The object to search.
 * @param {string} key - The key to match.
 * @param {string} value - The value to match.
 * @param {array} resultArray - An array to store matching objects.
 * @returns {array} - An array containing all objects matching the key-value pair.
 */
export function findAllObjectsByKeyValue(obj, key, value, resultArray = []) {
  // Check if the current object has the specified key-value pair
  if (obj[key] === value) {
    resultArray.push(obj) // Add the matching object to the result array
  }

  // Iterate through all properties of the object
  for (const prop in obj) {
    // Recursively call the function for nested objects
    if (obj[prop] !== null && typeof obj[prop] === 'object') {
      findAllObjectsByKeyValue(obj[prop], key, value, resultArray)
    }
  }

  return resultArray
}

/**
 *  Function to add trailing slash to the slug if it doesn't have one
 */
export function urlProcessor(url: string) {
  let modifiedUrl = url
  if (!url) {
    return '#'
  }

  if (url[url.length - 1] !== '/') {
    modifiedUrl = url + '/'
  }
  if (url[0] !== '/') {
    modifiedUrl = '/' + modifiedUrl
  }

  if (url === 'home') {
    modifiedUrl = '/'
  }
  return modifiedUrl
}

export const isBannerVisible = (
  props: { [k: string]: any },
  pathname: string
) => {
  if (props.isEnabled) {
    if (props.isGlobal) {
      return true // Show on all pages
    }

    if (!pathname) {
      return false
    }

    const currentPageSlug = pathname
    //
    //
    //
    const pageCategory = pathname?.split('/')[1]
    //
    //

    if (
      (props.SpecificPage &&
        props.SpecificPage.items.length &&
        currentPageSlug) ||
      (props.categoriesPage &&
        props.categoriesPage.items.length &&
        currentPageSlug)
    ) {
      const isSlugPresentSpecific = props.SpecificPage.items.some(
        (item) => urlProcessor(item.slug) === currentPageSlug
      )

      const isSlugPresentCategory = props.categoriesPage.items.some((item) =>
        item.slug.startsWith(pageCategory)
      )

      return isSlugPresentSpecific || isSlugPresentCategory
    }
  }

  return false // Don't show the banner if isEnable is false
}


/**
 * @description
 * A hook to get the translated string for given key, and locale.
 *
 * @param {keyof typeof enDictionary} key - The key of the string to be translated
 * @param {string} [locale='en-CA'] - The locale to get the translated string
 * @returns {string} - The translated string, or throws an error if the key is not found in the dictionary
 *
 * @example
 * const translatedString = useTranslation('minRead')
 */
export function useTranslation(
  key: keyof typeof enDictionary,
  locale: string = 'en-CA'
): string {
  let dictionary: { [k: string]: string } = {}

  switch (locale) {
    case 'en-CA':
      dictionary = enDictionary
      break
    case 'fr-CA':
      dictionary = frDictionary
      break

    case 'de-DE':
      dictionary = deDictionary
      break

    case 'es':
      dictionary = esDictionary
      break

    case 'it':
      dictionary = itDictionary
      break

    case 'nl':
      dictionary = nlDictionary
      break

    default:
      dictionary = enDictionary
      break
  }

  if (dictionary[key]) {
    return dictionary[key]
  } else {
    throw new Error(`Translation for key: ${key} not found in dictionary`)
  }
}

export function splitByFirstCapital(str: string) {
  // Match the position before an uppercase letter
  const index = str.search(/[A-Z]/)

  // If no uppercase letter is found or it's the first character, return the original string in an array
  if (index <= 0) {
    return [str]
  }

  // Split the string into two parts and return them in an array
  return [str.substring(0, index), str.substring(index)]
}

export const getBranch = () => {
  return {
    branch: process.env.VERCEL_BRANCH_URL?.split('-')?.[4],
    domain: process.env.VERCEL_BRANCH_URL?.split('-')?.[1],
  }
}

export const isProduction = () =>
  process.env.NEXT_PUBLIC_VERCEL_ENV === 'production'

export const extractErroredComponentFilePathAndLineNumber = (
  errorStack: string
) => {
  const regex = /(\.\/src\/[^\s:]+):(\d+):(\d+)/g
  const matches = []
  let match

  while ((match = regex.exec(errorStack)) !== null) {
    matches.push({
      filePath: match[1],
      lineNumber: match[2],
      columnNumber: match[3],
    })
  }

  return matches[0]
}

// Function to replace a key in an object
export function replaceKey(
  obj: Record<string, string>,
  oldKey: string,
  newKey: string
) {
  if (oldKey in obj) {
    obj[newKey] = obj[oldKey]
    delete obj[oldKey]
  }
  return obj
}

export const usaStates = [
  'Alabama',
  'Alaska',
  'Arizona',
  'Arkansas',
  'California',
  'Colorado',
  'Connecticut',
  'Delaware',
  'Florida',
  'Georgia',
  'Hawaii',
  'Idaho',
  'Illinois',
  'Indiana',
  'Iowa',
  'Kansas',
  'Kentucky',
  'Louisiana',
  'Maine',
  'Maryland',
  'Massachusetts',
  'Michigan',
  'Minnesota',
  'Mississippi',
  'Missouri',
  'Montana',
  'Nebraska',
  'Nevada',
  'New Hampshire',
  'New Jersey',
  'New Mexico',
  'New York',
  'North Carolina',
  'North Dakota',
  'Ohio',
  'Oklahoma',
  'Oregon',
  'Pennsylvania',
  'Rhode Island',
  'South Carolina',
  'South Dakota',
  'Tennessee',
  'Texas',
  'Utah',
  'Vermont',
  'Virginia',
  'Washington',
  'West Virginia',
  'Wisconsin',
  'Wyoming',
]
// regex patterns to match vercel app urls
export const corsAllowedPattern = /^https:\/\/.*-altus\.vercel\.app$/

// list of allowed origins
export const corsAllowedOrigins = [
  'http://localhost:3001',
  'https://msa-com-metamorphosis-v3a-git-dv-with-cross-post-a-5f3207-altus.vercel.app',
  'https://msa-com-metamorphosis-v3a-git-experiment-dv-notifi-ebcfb6-altus.vercel.app',
  'https://msa-com-metamorphosis-v3a-git-ace-v2-altus.vercel.app',
  'https://msa-com-metamorphosis-v3a-git-ace-v2-dev-altus.vercel.app',
  'https://contentful-translation-app.vercel.app',
  'https://msa-com-metamorphosis-v3a-git-ace-v2-test-altus.vercel.app',
  'https://msa-com-metamorphosis-v3a-git-exp-final-altus.vercel.app',
  'https://msa-cms-translations-v3a-git-dev-altus.vercel.app',
]

export const mktCloudAllowedOrigins = [
  'https://www.altusgroup.com',
  'https://www.verifino.com',
  'https://financeactive.com',
  'https://www.reonomy.com',
  'https://www.one11advisors.com',
  'https://www.forbury.com',
  'https://msa-agl-v3w-git-main-preview-altus.vercel.app',
  'https://msa-reo-v3w-git-main-preview-altus.vercel.app',
  'https://msa-fia-v3w-git-main-preview-altus.vercel.app',
  'https://msa-ver-v3w-git-main-preview-altus.vercel.app',
  'https://msa-o11-v3w-git-main-preview-altus.vercel.app',
  'https://msa-for-v3w-git-main-preview-altus.vercel.app',
  'https://msa-agl-v3w-git-staging-altus.vercel.app',
  'https://msa-reo-v3w-git-staging-altus.vercel.app',
  'https://msa-fia-v3w-git-staging-altus.vercel.app',
  'https://msa-ver-v3w-git-staging-altus.vercel.app',
  'https://msa-o11-v3w-git-staging-altus.vercel.app',
  'https://msa-for-v3w-git-staging-altus.vercel.app',
  'https://msa-agl-v3w-git-dev-altus.vercel.app',
  'https://msa-reo-v3w-git-dev-altus.vercel.app',
  'https://msa-fia-v3w-git-dev-altus.vercel.app',
  'https://msa-ver-v3w-git-dev-altus.vercel.app',
  'https://msa-o11-v3w-git-dev-altus.vercel.app',
  'https://msa-for-v3w-git-dev-altus.vercel.app',
  'https://msa-agl-v3w-git-staging-preview-altus.vercel.app',
  'https://msa-reo-v3w-git-staging-preview-altus.vercel.app',
  'https://msa-fia-v3w-git-staging-preview-altus.vercel.app',
  'https://msa-ver-v3w-git-staging-preview-altus.vercel.app',
  'https://msa-o11-v3w-git-staging-preview-altus.vercel.app',
  'https://msa-for-v3w-git-staging-preview-altus.vercel.app',
  'https://msa-agl-v3w-git-video-thumbnail-altus.vercel.app',
]

export function getCleanImageName(imageUrl: string): string {
  if (!imageUrl) {
    return ''
  }
  // Extract the file name from the URL
  const fileNameWithExtension = imageUrl.substring(
    imageUrl.lastIndexOf('/') + 1
  )

  // Remove the file extension (.png, .jpg, etc.)
  const fileName = fileNameWithExtension.substring(
    0,
    fileNameWithExtension.lastIndexOf('.')
  )

  // Remove underscores, special characters, and numeric values
  const cleanName = fileName
    .replace(/[\W_0-9]+/g, ' ') // Replace non-alphabetical characters and numbers with a space
    .trim() // Remove leading and trailing spaces
    .replace(/\s+/g, ' ') // Replace multiple spaces with a single space

  return cleanName
}

export function getDomainShortName(domain: string) {
  switch (domain) {
    case 'domainAltusGroupCom':
      return 'agl'
    case 'domainFinanceActiveCom':
      return 'fia'
    case 'domainReonomyCom':
      return 'reo'
    case 'domainVerifinoCom':
      return 'ver'
    case 'domainOne11Com':
      return 'o11'
    default:
      return ''
  }
}
export function generateAriaLabelFromSlug(
  slug: string,
  linkText: string = ''
): string {
  // Remove any leading slashes and split the slug by '-'
  let words = slug.replace(/-/g, ' ').replace(/^\//, '').split('/')

  // Create a meaningful phrase (example for a blog or about-us page)
  let label = words.join(' ')

  if (label === '#') {
    return `Open ${linkText} popup`
  } else if (label.startsWith('#')) {
    return `scroll to ${linkText}`
  } else if (label.includes('insights')) {
    return `Link ${linkText}, Read the article: ${label.replace('insights', '').trim()}`
  } else {
    return `Link ${linkText}, Visit the page ${label.trim()}`
  }
}

export function cl(...args) {
  if (process.env.NEXT_PUBLIC_LOG === 'true') {
    console.log(...args, '\n\n++++++++++++ CL Function Log ++++++++++++++++')
  }
}
export function isNumber(value) {
  return !isNaN(parseFloat(value)) && isFinite(value)
}

export function downloadFileLink(url: string) {
  if (!isBrowser()) return
  const a = document.createElement('a')
  a.href = url
  // a.target = "_blank";
  // a.download = fileName ?? "download"; // Filename for the downloaded file
  document.body.appendChild(a)
  a.click()
  a.remove()
  window.URL.revokeObjectURL(url) // Clean up the URL
}
export const ENV_VARS = {
  FIREBASE_API_KEY: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || '',
  FIREBASE_AUTH_DOMAIN: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || '',
  FIREBASE_PROJECT_ID: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || '',
  FIREBASE_STORAGE_BUCKET:
    process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || '',
  FIREBASE_MESSAGING_SENDER_ID:
    process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
  FIREBASE_APP_ID: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '',
  FIREBASE_MEASUREMENT_ID:
    process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || '',
  FIREBASE_DATABASE_URL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL || '',
  FIREBASE_VAPID_KEY: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || '',
  FIREBASE_PRIVATE_KEY: process.env.NEXT_PUBLIC_FIREBASE_PRIVATE_KEY || '',
  FIREBASE_CLIENT_EMAIl: process.env.NEXT_PUBLIC_FIREBASE_CLIENT_KEY || '',
  FIREBASE_API_SECRET: process.env.FIREBASE_API_SECRET || '',
}

export const suppressLogs = (): void => {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
    window.onerror = function () {
      // Prevent default logging of errors
      return true
    }

    window.onunhandledrejection = function () {
      // Prevent logging of unhandled promise rejections
      return true
    }

    console.log = () => { }
    console.debug = () => { }
    console.info = () => { }
    console.warn = () => { }
    console.error = () => { }
  } else {
    console.info = (...args) => {
      if (
        args.some((arg) => typeof arg === 'string' && arg.includes('tiptap'))
      ) {
        return
      }
    }

    console.warn = (...args) => {
      if (
        args.some((arg) => typeof arg === 'string' && arg.includes('tiptap'))
      ) {
        return
      }
    }
  }
}

export const isSafari = () => {
  const ua = navigator.userAgent
  const isSafariBrowser = /^((?!chrome|android).)*safari/i.test(ua)
  return isSafariBrowser
}

export function debounce<T extends (...args: any[]) => void>(
  fn: T,
  delay: number
) {
  let timeoutId: ReturnType<typeof setTimeout> | null = null

  return (...args: Parameters<T>) => {
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

// Map continent codes to your defined business regions.
export const CONTINENT_REGION_MAP: Record<string, 'APAC' | 'EMEA' | 'AMER'> = {
  // Americas
  NA: 'AMER',
  SA: 'AMER',
  // EMEA
  EU: 'EMEA',
  AF: 'EMEA',
  // APAC
  AS: 'APAC',
  OC: 'APAC',
}

export const REGION_TO_SLUG_MAP: Record<string, string> = {
  APAC: 'au',
  EMEA: 'uk',
  AMER: 'us',
}


export const getClientSideCookie = (name: string): string | undefined => {
  const cookieValue = document?.cookie
    ?.split('; ')
    ?.find((row) => row?.startsWith(`${name}=`))
    ?.split('=')?.[1]

  return cookieValue
}

export const EXCLUDED_KEYS = [
  'lang',
  'variant',
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_term',
  'utm_content',
  'gclid',
  'fbclid',
  'msclkid',
]

export const DYNAMIC_PAGE_PREFIX = 'land'
// if you are changing this then same should be changes in meta morphes app as well update the route name in nextjs as well where you are using this
