import 'moment/locale/de'
import 'moment/locale/fr'

import { VideoPlayerD } from '../../../archives/Components/Players/VideoPlayer/defaults'
import { CarouselGenericI } from '../../components/Carousels/CarouselGeneric/interface'
import { CarouselGeneric2I } from '../../components/Carousels/CarouselGeneric2/interface'
import { CarouselHeroHorizontalI } from '../../components/Carousels/CarouselHero/CarouselHeroHorizontal/interface'
import { CarouselHeroVerticalI } from '../../components/Carousels/CarouselHero/CarouselHeroVertical/interface'
import { CarouselHomeI } from '../../components/Carousels/CarouselHome/interface'
import { CarouselImageVideoI } from '../../components/Carousels/CarouselImageVideo/interface'
import { CarouselTabsI } from '../../components/Carousels/CarouselTabs/interface'
import { CarouselTwoColumnGenericI } from '../../components/Carousels/CarouselTwoColumn/CarouselTwoColumnGeneric/interface'
import { CarouselTwoColumnLogoI } from '../../components/Carousels/CarouselTwoColumn/CarouselTwoColumnLogo/interface'
import { CarouselWrapperI } from '../../components/Carousels/CarouselWrapper/interface'
import { CarouselTabBoxI } from '../../components/Carousels/CarouseTabBox/interface'
import { SimpleHeadingD } from '../../components/ContentBlocks/Texts/Headings/SimpleHeading/defaults'
import { lowerCaseFirstLetter } from '../../systems/AFS/AFSPages/utils'
import {
  getHeroHomeProps,
  getHeroSingleDisplayProps,
  getHeroSingleGenericProps,
  getHeroSingleLowerProps,
  getHeroTwoColumnProps,
} from './hero.mapping'
import { mapDataToComponent } from './index.mapping'
import { getPrimaryLinkProps } from './link.mapping'

export function getCarouselTwoColLogoProps(props: any): CarouselTwoColumnLogoI {
  const heroTwoColProps = props?.carouselItemsCollection.items.map((prop) =>
    getHeroTwoColumnProps(prop[`${lowerCaseFirstLetter(prop.__typename)}`])
  )

  let logos: any[] = []
  if (props?.carouselItemsCollection?.items) {
    logos = props?.carouselItemsCollection?.items?.map(
      (item) => item?.heroComponent?.logo
    )
  }

  const carouselProps: CarouselTwoColumnLogoI = {
    heading: props?.heading,
    // indicatorsPosition: 'Bottom',
    carouselData: heroTwoColProps,
    logo: logos,
    timer: (props?.timer ?? 5) * 1000,
    htmlAttr: props?.htmlAttr,
  }
  return carouselProps
}

export function getCarouselTwoColGenericProps(
  props: any
): CarouselTwoColumnGenericI {
  const heroTwoColProps = props?.carouselItemsCollection?.items?.map((prop) =>
    getHeroTwoColumnProps(prop?.[`${lowerCaseFirstLetter(prop?.__typename)}`])
  )

  const carouselProps: CarouselTwoColumnGenericI = {
    heading: props?.heading,
    // indicatorsPosition: 'Bottom',
    carouselData: heroTwoColProps,

    timer: (props?.timer ?? 5) * 1000,
    htmlAttr: props?.htmlAttr,
  }
  return carouselProps
}

export function getCarouselHeroVerticalProps(
  props: any
): CarouselHeroVerticalI {
  const heroVerticalProps = props?.carouselItemsCollection?.items?.map(
    (prop) => {
      let getHeroFunction
      const heroProps = prop?.[`${lowerCaseFirstLetter(prop?.__typename)}`]

      switch (heroProps?.template) {
        case 'SingleDisplay':
          getHeroFunction = getHeroSingleDisplayProps
          break
        case 'SingleGeneric':
          getHeroFunction = getHeroSingleGenericProps
          break
        case 'SingleLower':
          getHeroFunction = getHeroSingleLowerProps
        // Add more cases as needed

        default:
          return heroProps
      }

      return getHeroFunction(heroProps)
    }
  )
  const allTemplates =
    props?.carouselItemsCollection?.items?.map((item) => item?.template) || []
  const carouselProps: CarouselHeroHorizontalI = {
    heading: props?.heading,
    // indicatorsPosition: 'Bottom',
    carouselData: heroVerticalProps,
    timer: (props?.timer ?? 5) * 1000,
    htmlAttr: props?.htmlAttr,
    templates: allTemplates,
  }

  return carouselProps
}

export function getCarouselHeroHorizontalProps(
  props: any
): CarouselHeroHorizontalI {
  const heroHorizontalProps = props?.carouselItemsCollection?.items?.map(
    (prop) => {
      let getHeroFunction
      const heroProps = prop?.[`${lowerCaseFirstLetter(prop?.__typename)}`]
      // debugger
      switch (heroProps?.template) {
        case 'SingleDisplay':
          getHeroFunction = getHeroSingleDisplayProps
          break
        case 'SingleGeneric':
          getHeroFunction = getHeroSingleGenericProps
          break
        case 'SingleLower':
          getHeroFunction = getHeroSingleLowerProps
          break
        case 'HeroHome':
          getHeroFunction = getHeroHomeProps
          break
        // Add more cases as needed

        default:
          return heroProps
      }

      return getHeroFunction(heroProps)
    }
  )

  let tabHeadings = []
  if (props?.tabHeadings && props?.tabHeadings?.length !== 0) {
    tabHeadings = props?.tabHeadings?.map((heading: string) => ({
      textContent: heading,
    }))
  }

  const allTemplates =
    props?.carouselItemsCollection?.items?.map((item) => item?.template) || []
  const carouselProps: CarouselHeroHorizontalI = {
    heading: props?.heading,
    // indicatorsPosition: 'Bottom',
    carouselData: heroHorizontalProps,
    timer: (props?.timer ?? 5) * 1000,
    htmlAttr: props?.htmlAttr,
    templates: allTemplates,
    tabHeadings: tabHeadings,
  }

  return carouselProps
}

export function getCarouselGenericProps(props: any): CarouselGenericI {
  const carouselProps: CarouselGenericI = {
    htmlAttr: props?.htmlAttr,
    isLightMode: props?.isLightMode,
    timer: (props?.timer ?? 5) * 1000,
    contextualInformation: {
      heading: props?.heading
        ? {
            textContent: props?.heading,
            as: 'h2',
          }
        : undefined,
      subHeading: props?.subheading
        ? {
            textContent: props?.subheading,
          }
        : undefined,
      excerpt: {
        data: null,
      },
      showButtons: false,
    },
    button: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
    carouselData: props?.carouselItemsCollection?.items?.map((item) => {
      if (item) {
        let obj = item?.[lowerCaseFirstLetter(item?.__typename)] ?? {}
        if (Object.keys(obj).length !== 0) {
          return {
            media: {
              ...VideoPlayerD,
              videoId: obj?.contentId,
              image: obj?.image,
            },
            mediaHeading: obj?.heading
              ? {
                  ...SimpleHeadingD,
                  textContent: obj?.heading,
                }
              : undefined,
            mediaInfo: obj?.description
              ? {
                  data: obj?.description,
                }
              : undefined,
          }
        }
      }
    }),
  }
  return carouselProps
}

export function getCarouselHomeProps(props: any): CarouselHomeI {
  const carouselProps: CarouselHomeI = {
    ...props,
    htmlAttr: props?.htmlAttr,
    isLightMode: true,
    button: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
    description: {
      textContent: props?.heading,
    },
    carouselData: props?.carouselItemsCollection?.items.map((item) => {
      if (item) {
        let obj = item?.[lowerCaseFirstLetter(item?.__typename)] ?? {}
        if (Object.keys(obj)?.length !== 0) {
          return mapDataToComponent(obj)
        }
      }
    }),
  }
  return carouselProps
}

export function getCarouselTabBoxProps(props: any): CarouselTabBoxI {
  const carouselProps: CarouselTabBoxI = {
    htmlAttr: props?.htmlAttr,
    isLightMode: true,
    carouselData: props?.carouselItemsCollection?.items?.map((item) => {
      if (item) {
        let obj = item?.[lowerCaseFirstLetter(item?.__typename)] ?? {}
        if (Object.keys(obj)?.length !== 0) {
          return mapDataToComponent(obj)
        }
      }
    }),
  }
  return carouselProps
}

export function getCarouselGeneric2Props(props: any): CarouselGeneric2I {
  const carouselProps: CarouselGeneric2I = {
    htmlAttr: props?.htmlAttr,
    isLightMode: props?.isLightMode,
    isFullXBleed: props?.isFullBleed,
    timer: (props?.timer ?? 5) * 1000,
    contextualInformation: {
      heading: {
        textContent: props?.heading,
        as: 'h2',
      },
      subHeading: {
        textContent: props?.subheading,
      },
      excerpt: {
        data: props?.description,
      },
      showButtons: false,
    },
    button: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
    carouselData: props?.carouselItemsCollection?.items?.map((item) => {
      if (item) {
        let obj = item?.[lowerCaseFirstLetter(item?.__typename)] ?? {}
        if (Object.keys(obj)?.length !== 0) {
          return mapDataToComponent({ ...obj, isLightMode: props?.isLightMode })
        }
      }
    }),
  }
  return carouselProps
}

export function getCarouselImageVideoProps(props: any): CarouselImageVideoI {
  const carouselProps: CarouselImageVideoI = {
    htmlAttr: props?.htmlAttr,
    isLightMode: props?.isLightMode,
    isFullXBleed: true, // props?.isFullBleed,
    isCenterItemHighlighted:
      props?.isCenterItemHighlighted?.includes('Highlight the center item') ??
      false,
    contextualInformation: {
      heading: {
        textContent: props?.heading,
        as: 'h2',
      },
      subHeading: {
        textContent: props?.subheading,
      },
      excerpt: {
        data: props?.description,
      },
      showButtons: false,
    },
    button: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
    carouselData: props?.carouselItemsCollection?.items?.map((item) => {
      if (item) {
        let obj = item?.[lowerCaseFirstLetter(item?.__typename)] ?? {}
        if (item?.__typename === 'Player' && Object.keys(obj)?.length !== 0) {
          return {
            media: mapDataToComponent({
              ...obj,
              isLightMode: props?.isLightMode,
            }),
            mediaHeading: obj?.heading && {
              ...SimpleHeadingD,
              textContent: obj?.heading,
            },
            mediaInfo: obj?.description && {
              data: obj?.description,
            },
          }
        } else if (
          item?.__typename === 'ComponentImage' &&
          Object.keys(obj)?.length !== 0
        ) {
          return {
            media: mapDataToComponent({
              ...obj,
              isLightMode: props?.isLightMode,
            }),
            mediaHeading: {
              ...SimpleHeadingD,
              textContent: obj?.title,
            },
            mediaInfo: {
              data: obj?.description,
            },
          }
        }
      }
    }),
  }
  return carouselProps
}

export function getCarouselTabsProps(props: any): CarouselTabsI {
  const items = props?.carouselItemsCollection?.items?.map((prop) => {
    const itemProps = prop?.[`${lowerCaseFirstLetter(prop?.__typename)}`]
    return mapDataToComponent(itemProps)
  })

  let tabHeadings = []
  if (props?.tabHeadings && props?.tabHeadings?.length !== 0) {
    tabHeadings = props?.tabHeadings?.map((heading: string) => ({
      textContent: heading,
    }))
  }

  const carouselProps: CarouselTabsI = {
    heading: props?.heading,
    carouselData: items,
    timer: props?.timer ? props?.timer * 1000 : undefined,
    htmlAttr: props?.htmlAttr,
    tabHeadings: tabHeadings,
  }

  return carouselProps
}
export function getCarouselWrapperProps(props: any): CarouselWrapperI {
  const carouselProps: CarouselWrapperI = {
    htmlAttr: props?.htmlAttr,
    isLightMode: props?.isLightMode ?? true,
    format: props?.carouselFormat ?? 'Constrained',
    numberOfItemsVisible: props?.numberOfItemsVisible,
    indicatorPosition: props?.indicatorPosition,
    subheading: {
      textContent: props?.subheading,
    },
    description: {
      data: props?.description,
    },
    carouselData: props?.carouselItemsCollection?.items?.map((item) => {
      if (item) {
        let obj = item?.[lowerCaseFirstLetter(item?.__typename)] ?? {}
        if (Object.keys(obj)?.length !== 0) {
          return mapDataToComponent({ ...obj, isLightMode: props?.isLightMode })
        }
      }
    }),
  }
  return carouselProps
}