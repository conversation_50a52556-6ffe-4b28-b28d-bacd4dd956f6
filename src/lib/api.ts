export async function fetchGraphQL(
  query: string,
  preview = false,
  revalidateTag?: string,
  retries = 5
): Promise<unknown> {
  preview = process.env.NEXT_PUBLIC_PREVIEW === 'true'
  const contentfulAccessToken =
    process.env.CONTENTFUL_ACCESS_TOKEN ??
    process.env.NEXT_PUBLIC_CONTENTFUL_ACCESS_TOKEN

  let nextConfig = {}
  if (revalidateTag) {
    nextConfig = { tags: [revalidateTag] }
  }

  const response = await fetch(
    `https://graphql.contentful.com/content/v1/spaces/${process.env.NEXT_PUBLIC_CONTENTFUL_SPACE_ID}/environments/${process.env.NEXT_PUBLIC_CONTENTFUL_ENVIRONMENT}`,
    {
      // cache: 'no-store',
      next: nextConfig,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${contentfulAccessToken}`,
      },
      body: JSON.stringify({ query }),
    }
  )

  let responseData = await response.json()

  if (responseData.errors) {
    if (responseData.errors?.[0]?.message.includes('rate limit')) {
      if (retries > 0) {
        console.error(
          'Retry counter: ',
          5 - retries,
          ' API::error',
          responseData
        )
        await new Promise((resolve) => setTimeout(resolve, 500))
        responseData = await fetchGraphQL(
          query,
          preview,
          revalidateTag,
          retries - 1
        )
      } else {
        throw new Error('API::error: retries exceeded')
      }
    }
  }

  return responseData
}

export async function getEntryDataById(entryId: string) {
  const {
    CONTENTFUL_ACCESS_TOKEN,
    NEXT_PUBLIC_CONTENTFUL_ACCESS_TOKEN,
    NEXT_PUBLIC_CONTENTFUL_SPACE_ID,
    NEXT_PUBLIC_CONTENTFUL_ENVIRONMENT,
    NODE_ENV,
  } = process.env

  const contentfulAccessToken =
    CONTENTFUL_ACCESS_TOKEN ?? NEXT_PUBLIC_CONTENTFUL_ACCESS_TOKEN
  try {
    const response = await fetch(
      `https://preview.contentful.com/spaces/${NEXT_PUBLIC_CONTENTFUL_SPACE_ID}/environments/${NEXT_PUBLIC_CONTENTFUL_ENVIRONMENT}/entries/${entryId}?access_token=${contentfulAccessToken}`
    )
    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.statusText}`)
    }
    const result: GraphQLResponse<T> = await response.json()
    if (result?.errors) {
      console.error('GraphQL errors:', result.errors)
    }
    return result
  } catch (error) {
    console.log(error)
  }
}