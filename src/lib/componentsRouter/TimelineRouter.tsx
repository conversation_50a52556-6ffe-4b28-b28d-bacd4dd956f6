import TimelineMilestonesWithImage from '../../components/Timeline/TimelineMilestonesWithImage'
import TimelineMilestonesWithText from '../../components/Timeline/TimelineMilestonesWithText'
import { TimelineProps } from '../../components/Timeline/types'

const TimelineRouter = (props: TimelineProps) => {
  // let carouselProps = null
  let template = props?.template || 'TimelineMilestonesWithText'
  switch (template) {
    case 'Milestones With Image':
      const updatedProps = props as TimelineMilestoneItemsProps
      return <TimelineMilestonesWithImage {...updatedProps} />
    case 'Milestones With Text':
      const updatedTextProps = props as TimelineMilestonesWithTextProps
      return <TimelineMilestonesWithText {...updatedTextProps} />
    // console.warn('TimelineMilestonesWithText is not implemented yet.')
    // return null
    // case 'TimelineMilestonesWithGeneric':
    //   // return <TimelineMilestonesWithGeneric {...props} />;
    //   console.warn('TimelineMilestonesWithGeneric is not implemented yet.')
    //   return null

    default:
      console.warn(`No component found for template: ${props.template}`)
      return null
  }
}

export default TimelineRouter
