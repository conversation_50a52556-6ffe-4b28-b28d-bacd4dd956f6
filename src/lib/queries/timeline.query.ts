export const timelineQuery = (id: string, locale: string) => `
{
  timeline(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${timelineFragment}
  }
}
`

export const timelineFragment = `
  ... on Timeline {
      __typename
      sys{
        id
      }
      title
      internalName
      template
      htmlAttr
      timelineItemsCollection{
        items {
          __typename
          heading
          subHeading
          description
          cardTextContent
          startDate
          location{
           lat
           lon
          }
          milestoneImage {
          title
          url
         }   
         
      }
    }
  }
`

export const timelineItemQuery = (id: string, locale: string) => `
{
  timelineItem(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${timelineItemFragment}
  }
}
`

export const timelineItemFragment = `
  ... on TimelineItem {
      __typename
      sys{
        id
      }
      internalName
      timelineItemComponentsCollection{
        items {
      	...on Entry{
            __typename
            sys{
              id
            }
          }
      }
    }
  }
`
