import { Metadata } from 'next'
import { BUISNESS_DOMAINS } from '.'
import { getRedirects } from '../../actions/getRedirects'
import { mapNotifications } from '../components/Notifications/notificationutils'
import {
  BUISNESS_DOMAINS_LOCALE_NAVIGATION,
  BUSINESS_DOMAINS_TITLE,
  LOCALE_CONSTANT,
  TAG_TO_URL,
} from '../globals/utils'
import { fetchGraphQL } from '../lib/api'
import {
  findProcessAndPlaceObjects,
  getComponentQueryData,
} from '../lib/propsMapping/index.mapping'
import { navigationFooterQuery } from '../lib/queries/navigationFooter.query'
import { navigationPrimaryHeaderQuery } from '../lib/queries/navigationHeader.query'
import {
  getIdFromSlugQuery,
  getPageFromSlugQuery,
  pageLocaleSlugById,
  pageUrlQueryByLocale,
} from '../lib/queries/page.query'

export async function metadataProcessor(params: PageProps['params']) {
  let locale = LOCALE_CONSTANT[params?.slug?.[0]] || 'en-CA'
  const currentDomain = process.env.NEXT_PUBLIC_DOMAIN
  if (BUISNESS_DOMAINS['altus'] === currentDomain) {
    if (params?.slug?.[params?.slug?.length - 1] === 'fr') {
      params.slug?.pop()
      locale = LOCALE_CONSTANT['fr']
    }
  }
  const fullUrl = params?.slug?.join('/') || '/'

  const response = await fetchGraphQL(
    getPageFromSlugQuery(fullUrl, locale),
    true
  )
  const pageData = response?.data?.pageCollection?.items?.at(0)
  const pageId = pageData?.sys?.id

  // get the canonical url from page data as specified in contentful
  let canonicalUrl = pageData?.canonicalUrl

  if (!canonicalUrl) {
    // if no canonical url is specified from the contentful
    if (
      // if the domain is altus and the locale is not english, then we don't need to set the canonical url as slugs are same for both lang
      !(
        currentDomain === BUISNESS_DOMAINS['altus'] &&
        locale !== LOCALE_CONSTANT['en']
      )
    ) {
      canonicalUrl = fullUrl // set the canonical url to the current url, so it is marked as self canonical
    }
  }

  if (!pageData) {
    return {
      title: BUSINESS_DOMAINS_TITLE[currentDomain as string] + ' | 404',
    }
  }

  let redirects = []

  if (currentDomain === BUISNESS_DOMAINS['finance']) {
    // In your component or page
    redirects = await getJsonCsvData('fia-redirects.csv')
  }

  if (currentDomain === BUISNESS_DOMAINS['verifino']) {
    // In your component or page
    redirects = await getJsonCsvData('ver-redirects.csv')
  }

  // console.log('redirects: ', redirects)
  let hrefLangs: Record<string, string> = {}
  if (currentDomain === BUISNESS_DOMAINS['altus']) {
    hrefLangs = {
      en: fullUrl,
      fr: fullUrl + '/?lang=fr',
    }
  } else if (currentDomain !== BUISNESS_DOMAINS['reonomy']) {
    const allLocaleSlugsResponse = await fetchGraphQL(
      pageLocaleSlugById(pageId),
      true
    )
    const allLocaleSlugsData = allLocaleSlugsResponse?.data ?? {}

    BUISNESS_DOMAINS_LOCALE_NAVIGATION[currentDomain].forEach((locale) => {
      if (locale !== 'en-CA') {
        const onlyLang = locale?.split('-')?.[0]
        const slug = allLocaleSlugsData?.[onlyLang]?.slug

        if (slug) {
          hrefLangs[onlyLang] =
            getSlugRedirectPath({ jsonData: redirects, slug }) ?? slug
        }
      }
    })
  }

  let content: Metadata = {
    title:
      pageData?.seoTitle || BUSINESS_DOMAINS_TITLE[currentDomain as string],
    description:
      pageData?.seoDescription ||
      BUSINESS_DOMAINS_TITLE[currentDomain as string],
    // keywords: pageData?.seoKeywords,
    ////// other metadata options///////
    // creator: 'Jiachi Liu',
    // publisher: 'Sebastian Markbåge',
    robots: {
      index:
        !pageData?.noIndex ||
        (!!pageData?.isExperimentation && !!pageData?.experimentationId),
      follow:
        !pageData?.noFollow ||
        (!!pageData?.isExperimentation && !!pageData?.experimentationId),
      // nocache: true,
      // googleBot: {
      //   index: true,
      //   follow: false,
      //   noimageindex: true,
      //   'max-video-preview': -1,
      //   'max-image-preview': 'large',
      //   'max-snippet': -1,
      // },
    },
    openGraph: {
      title: pageData?.seoTitle,
      description: pageData?.seoDescription,
      publishedTime: pageData?.publishDate,
      url: fullUrl,
      // siteName: 'Next.js',
      images: [pageData?.pageThumbnail?.url],
      // image: pageData?.pageThumbnail?.url,
      // locale: 'en_US',
      // type: 'website',
    },
    metadataBase: new URL(TAG_TO_URL[currentDomain]),
    alternates: {
      canonical: canonicalUrl,
      languages: hrefLangs,
    },
    twitter: {
      // card: 'app',
      title: pageData?.seoTitle,
      description: pageData?.seoDescription,
      // siteId: '1467726470533754880',
      // creator: '@nextjs',
      // creatorId: '1467726470533754880',
      images: [pageData?.pageThumbnail?.url],
    },
  }

  if (currentDomain === BUISNESS_DOMAINS['altus'] && fullUrl === '/') {
    content = getAltusHomeMetadata(content)
  }

  if (
    pageData?.template === 'Press Release' ||
    pageData?.template === 'Insight Article'
  ) {
    const authros = pageData?.authorsCollection?.items.map((e) => {
      return e?.fullName || ''
    })

    content.authors = {
      name: authros,
    }
  }
  return content
}

export const getSlugRedirectPath = ({
  jsonData,
  slug,
}: {
  slug: string
  jsonData: { [sourcePath: string]: string }
}) => {
  if (jsonData[`/${slug}/`])
    return jsonData[`/${slug}/`]?.replace(/^\/|\/$/g, '')
  else slug
}

/**
 * Fetches a csv file from the redirects folder and returns it as a json object
 * @param {string} filename the name of the csv file
 * @returns {Promise<Record<string,string>>} a json object where the keys are the source paths and the values are the destination paths
 */
export async function getJsonCsvData(filename: string) {
  const data: any = await getRedirects({ filename })

  let jsonCsvData = data
    .map((item: { sourcePath: string; destinationPath: string }) => {
      return { [item.sourcePath]: item.destinationPath }
    })
    .reduce((a: any, b: any) => Object.assign(a, b), {})

  return jsonCsvData
}

function getAltusHomeMetadata(metadata: Metadata): Metadata {
  return {
    ...metadata,
    metadataBase: null,
    openGraph: {
      ...metadata.openGraph,
      url: TAG_TO_URL['domainAltusGroupCom'],
    },
    alternates: {
      ...metadata.alternates,
      languages: {
        en: TAG_TO_URL['domainAltusGroupCom'],
        fr: TAG_TO_URL['domainAltusGroupCom'] + '/?lang=fr',
      },
    },
  }
}

/** * @typedef {Object} PageProps
 * @property {Object} params - The parameters from the URL.
 * @property {string[]} params.slug - The slug from the URL, which is an array of strings.
 * @returns Promise<{ slug: string[] }[]>
 * This function generates static parameters for the dynamic route.
 * It fetches the page IDs from the database and returns them as an array of objects.
 * If the environment variable IS_SSG is set to 'false', it returns a default slug
 * with an empty array, which is used for client-side rendering.
 * If the business domain is 'altus', it generates slugs for both English and French
 * pages. Otherwise, it generates locale-specific slugs based on the business domain.
 */
export async function staticParamsProcessor() {
  if (process.env.IS_SSG === 'false') {
    return [
      {
        slug: [],
      },
    ]
  }

  if (BUISNESS_DOMAINS['altus'] === process.env.NEXT_PUBLIC_DOMAIN) {
    return await generateEnFrSlugs()
  } else {
    return await generateLocaleSpecificSlugs()
  }
}

async function generateEnFrSlugs() {
  const { data }: any = await fetchGraphQL(pageUrlQueryByLocale())
  const pages = data?.pageCollection

  return ['fr', '']
    .map((elem) =>
      pages?.items?.map((page: any) => ({
        slug: [
          ...page?.slug?.split('/')?.filter((item: string) => item !== ''),
          elem,
        ],
      }))
    )
    .flat()
}

async function generateLocaleSpecificSlugs() {
  const locales =
    BUISNESS_DOMAINS_LOCALE_NAVIGATION[process.env.NEXT_PUBLIC_DOMAIN!]
  const pagesData = []

  for (let i = 0; i < locales.length; i++) {
    const locale = locales[i]

    try {
      const { data } = await fetchGraphQL(
        pageUrlQueryByLocale(locale as string)
      )
      const pages = data?.pageCollection
      const mappedData = pages?.items?.map((page) => ({
        slug: [...page?.slug?.split('/')],
      }))
      pagesData?.push([mappedData])
    } catch (error) {
      throw error
    }
  }

  const resolvedData = await Promise.all(pagesData)

  let tempArray = resolvedData?.flat(2)
  tempArray = tempArray?.map((elem: any) => ({
    slug: elem?.slug?.filter((el: string) => el != ''),
  }))
  return tempArray
}

export const fetchAndFilterNotifications = async (fullUrl, locale) => {
  const notifications = await mapNotifications(fullUrl, locale)

  const carouselBanners = notifications
    ?.filter((item) => item?.template === 'Carousels')
    ?.flatMap((carousel) => carousel?.carouselDataCollection?.items || [])

  const filteredNotifications = notifications?.filter(
    (item) =>
      !carouselBanners?.some((banner) => banner?.sys?.id === item?.sys?.id)
  )

  const shouldRenderNotifications = filteredNotifications?.filter((item) => {
    const specificPages = item?.specificPageCollection?.items || []
    const categories = item?.categories || []
    const excludeCategories =
      item?.excludedPagesFromCategoryCollection?.items || []
    const isGlobal = item?.isGlobal

    const isInSpecificPages = specificPages?.some(
      (page) => page?.slug === fullUrl
    )
    const inCategories = categories?.some((category) =>
      fullUrl?.split('/')?.includes(category)
    )
    const isInExcludeCategories = excludeCategories?.some(
      (page) => page?.slug === fullUrl
    )

    return (
      isGlobal || isInSpecificPages || (inCategories && !isInExcludeCategories)
    )
  })

  return shouldRenderNotifications?.filter(Boolean)
}

export const fetchNavigationHeaderData = async (locale) => {
  const response = await fetchGraphQL(
    navigationPrimaryHeaderQuery(locale),
    true,
    'header'
  )
  await findProcessAndPlaceObjects({
    obj: response,
    locale,
    visitedIds: ['nav-header'],
  })
  return response?.data?.componentNavigationHeaderCollection?.items?.at(0) || {}
}

export const fetchNavigationFooterData = async (locale) => {
  const response = await fetchGraphQL(
    navigationFooterQuery(locale),
    true,
    'footer'
  )
  await findProcessAndPlaceObjects({
    obj: response,
    locale,
    visitedIds: ['nav-footer'],
  })
  return response?.data?.componentNavigationFooterCollection?.items?.at(0) || {}
}

export const fetchPageIdFromSlug = async (fullUrl, locale) => {
  const response = await fetchGraphQL(
    getIdFromSlugQuery(fullUrl, locale),
    true,
    'page'
  )
  return response?.data?.pageCollection?.items?.[0]?.sys?.id
}

export const fetchPageDataFromSlug = async (fullUrl, locale, pageId) => {
  return await fetchGraphQL(getPageFromSlugQuery(fullUrl, locale), true, pageId)
}

export const fetchComponentData = async (items, locale, fullUrl?: string) => {
  const promises = items?.map(async (componentData) => {
    return await getComponentQueryData({
      id: componentData?.sys?.id,
      typename: componentData?.__typename,
      locale,
      visitedIds: ['page'],
      fullUrl,
    })
  })
  const responses = await Promise.all(promises ?? [])
  return responses?.map((item) => (item ? Object.values(item)?.[0] : {}))
}

export const fetchLocalizationData = async (id) => {
  const { data } = await fetchGraphQL(pageLocaleSlugById(id), true, id)
  const langLocalization = {}
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const element = data[key]
      langLocalization[key] = {
        slug: element?.slug === '/' ? '/' : `/${element?.slug}`,
      }
    }
  }
  return langLocalization
}
