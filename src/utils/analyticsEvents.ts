import { getClient<PERSON>ide<PERSON><PERSON>ie, isBrowser } from "../globals/utils";

export function formSuccessEvent({
    contentfulId,
    formCategory,
    contactInquiryType = null,
    productInterest = null,
}) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'custom_form_success',
            formId: contentfulId,
            formCategory,
            contactInquiryType,
            productInterest
        })
    }
}

export function formErrorEvent({
    contentfulId,
    formCategory
}) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'custom_form_error',
            formId: contentfulId,
            formCategory
        })
    }
}

export function searchOpenEvent() {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'open_search_screen',
        })
    }
}

export function searchCloseEvent() {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'close_search_screen',
        })
    }
}

export function enteredSearchInputEvent() {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'entered_search_input',
        })
    }
}

export function searchResultClickEvent(href?: string) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'clicked_search_result',
            clicked_url: href
        })
    }
}

export function copyFilterEvent(copiedUrl: string) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'afs_copy_filter_url',
            copied_url: copiedUrl
        })
    }
}

export function clearAllFilterEvent() {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'afs_clear_all_filter',
        })
    }
}

export function sortByEvent(option: string) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'afs_sort_by',
            selected_option: option
        })
    }
}

export function afsMenuEvent(category: string, option: string) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'afs_menu',
            category: category,
            selected_option: option
        })
    }
}

export function buttonClickEvent(btnText, url) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'button_click',
            button_text: btnText,
            clicked_url: url
        })
    }
}

export const CTA_INTENTS = {
    "OPEN_POPUP": "OPEN_POPUP",
    "GET_INFO": "GET_INFO", // contact / mail / phone
    "GO_TO": "GO_TO",
    "UNCLASSIFIED": "UNCLASSIFIED"
}

export interface ICtaClickEventProps {
    intent: keyof typeof CTA_INTENTS,
    intentObjectInfo: string,
    ctaContent?: string
}

export function ctaClickEvent({
    ctaContent,
    intent,
    intentObjectInfo
}: ICtaClickEventProps) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'event_click', // there are multiple click events saved in tagmanager, which will be revisited in future when we optimise events in future
            intent: intent,
            intent_object_info: intentObjectInfo,
            cta_content: ctaContent, // icon_{icon name} || button text
        })
    }
}

// If UNCLASSIFIED
// if a then href > URL / Section Id // if button or other tag>  tagname + classes\


export function getMeasurementId() {
    const scripts = Array.from(document?.scripts || []);
    for (const script of scripts) {
        const src = script?.src || '';
        const match = src?.match(/https:\/\/www.googletagmanager.com\/gtag\/js\?id=(G-[A-Z0-9]+)/);
        if (match?.[1]) return match[1];
    }
    return null;
}

export function getClientIdFromCookie() {
    const gaCookie = getClientSideCookie('_ga');

    if (!gaCookie) return null;

    const parts = gaCookie?.split('.');
    if (parts?.length >= 4) {
        return `${parts[2]}.${parts[3]}`; // This is the client_id
    }

    return null;
}