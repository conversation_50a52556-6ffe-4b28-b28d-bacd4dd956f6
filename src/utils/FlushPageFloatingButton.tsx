'use client'

import { useEffect, useState } from 'react'

const FlushPageFloatingButton = () => {
    const [error, setError] = useState<string | null>(null)
    const [loading, setLoading] = useState<boolean>(false)
    const [success, setSuccess] = useState<string | null>(null)

    // Function to reset the states after a delay
    useEffect(() => {
        if (success || error) {
            const timer = setTimeout(() => {
                setError(null)
                setSuccess(null)
            }, 5000) // Reset to normal after 2 minutes
            return () => clearTimeout(timer) // Cleanup timeout
        }
    }, [success, error])

    const FlushPage = async () => {
        setLoading(true)
        setError(null)
        setSuccess(null)

        try {
            if (typeof window === 'undefined') {
                throw new Error('Window is not available')
            }
            const url = window.location.href
            const parsedUrl = new URL(url)

            // Extract base vercelUrl
            const vercelUrl = `${parsedUrl.protocol}//${parsedUrl.host}`

            // Extract pathname and query parameters
            const pathname = parsedUrl.pathname.replace(/\/$/, '') // Remove trailing slash
            const lang = parsedUrl.searchParams.get('lang')

            // Determine slugs dynamically
            const slugs = lang
                ? [`${pathname}`, `${pathname}/${lang}`, `${pathname}/`, `${pathname}/${lang}/`]
                : [`${pathname}`, `${pathname}/`]

            // API call
            const response = await fetch(`${vercelUrl}/api/revalidate/page/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ slugs }),
            })
            const data = await response.json()

            if (data.revalidated) {
                setSuccess('Page updated successfully. Reloading...')
                console.log('Page revalidation succeeded', data)
                window.location.reload()
            } else {
                setError('Page revalidation failed')
                console.error('Page revalidation failed', data)
            }
        } catch (err) {
            setError('Page revalidation failed')
            console.error('Page revalidation failed', err)
        } finally {
            setLoading(false)
        }
    }

    return (
        <>
            {error && (
                <div
                    style={{
                        color: '#fff',
                        backgroundColor: '#dc3545',
                        padding: '10px',
                        borderRadius: '5px',
                        textAlign: 'center',
                    }}
                >
                    {error}
                </div>
            )}
            {success && (
                <div
                    style={{
                        color: '#fff',
                        backgroundColor: '#28a745',
                        padding: '10px',
                        borderRadius: '5px',
                        textAlign: 'center',
                    }}
                >
                    {success}
                </div>
            )}
            <div
                style={{
                    position: 'fixed',
                    bottom: '20px',
                    left: '20px',
                    zIndex: 1000,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: '10px',
                }}
            >
                <button
                    onClick={FlushPage}
                    disabled={loading}
                    style={{
                        backgroundColor: loading ? '#ccc' : '#56e5a9',
                        color: 'black',
                        border: '1px dashed #54661d',
                        borderRadius: '50%',
                        width: '50px',
                        height: '50px',
                        fontSize: '12px',
                        cursor: loading ? 'not-allowed' : 'pointer',
                        boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.2)',
                        transition: 'transform 0.2s ease',
                        transform: loading ? 'scale(0.95)' : 'scale(1)',
                    }}
                >
                    {loading ? '...' : 'Flush Page'}
                </button>
            </div>
        </>
    )
}

export default FlushPageFloatingButton