'use client'
import { usePathname } from 'next/navigation'
import { ReactNode, useEffect } from 'react'
import {
  getQueryParamJson,
  getSessionStorageItem,
  isProduction,
  setSessionStorageItem,
} from '../globals/utils'
import { deleteFormActivation } from '../redux/slices/formSlice'
import { useAppDispatch, useAppSelector } from '../redux/store'

const FormDataProvder = ({ children }: { children: ReactNode }) => {
  const isFormActivated = useAppSelector((state) => state.form.isFormActivated)
  const pathname = usePathname()
  const dispatch = useAppDispatch()

  const handle = () => {
    const sessionParams = JSON.parse(getSessionStorageItem('__params') ?? '{}')
    const temp = { ...getQueryParamJson(), ...sessionParams }

    if (temp.gclid?.length > 0) {
      temp.utm_medium = 'search-paid'
    }
    setSessionStorageItem('__params', JSON.stringify(temp))
  }

  const handleUTMParam = () => {
    const url = new URL(window.location.href)
    const searchParams = new URLSearchParams(url.search)

    let tempParams = getQueryParamJson()

    const isGoogleReferrer = document.referrer.includes('google.com')

    const hasUTM =
      searchParams.has('utm_source') || searchParams.has('utm_medium')

    if (!hasUTM && isGoogleReferrer) {
      searchParams.set('utm_source', 'google')
      searchParams.set('utm_medium', 'organic')

      const sessionParams = JSON.parse(
        getSessionStorageItem('__params') ?? '{}'
      )

      const temp = { ...sessionParams, ...tempParams }

      setSessionStorageItem('__params', JSON.stringify(temp))

      url.search = searchParams.toString()
      window.location.href = url.toString()
    }
  }

  function handleTabClose() {
    window.addEventListener('beforeunload', function () {
      // send custom form start event for all activated forms
      Object.keys(isFormActivated)?.forEach((formId) => {
        //@ts-ignore
        window.dataLayer?.push({
          event: 'custom_form_abandoned',
          formId,
        })
      })
    })
  }

  useEffect(() => {
    Object.keys(isFormActivated)?.forEach((formId) => {
      //@ts-ignore
      window.dataLayer?.push({
        event: 'custom_form_abandoned',
        formId,
      })
      dispatch(deleteFormActivation({ contentfulFormId: formId }))
    })

    function getParameterByName(name: string) {
      let allParams = Object.fromEntries(
        new URLSearchParams(window.location.search)
      )
      return allParams[name] || ''
    }

    const domain: string = process.env.NEXT_PUBLIC_DOMAIN

    if (
      isProduction() &&
      (domain === 'domainAltusGroupCom' || domain === 'domainReonomyCom')
    ) {
      let sfmc_sub = getParameterByName('sfmc_sub')
      window._etmc?.push(['setOrgId', '546007750'])
      if (sfmc_sub != '') {
        window._etmc?.push(['setUserInfo', { email: sfmc_sub }])
        console.log('SMFC_SUB=' + sfmc_sub)
      }
      let url = location.protocol + '//' + location.host + location.pathname
      console.log('URL=' + url)
      window._etmc?.push(['trackPageView', { item: url }])
    }
  }, [pathname])

  useEffect(() => {
    handle()
    handleUTMParam()
    handleTabClose()
  }, [])

  return children
}

export default FormDataProvder
